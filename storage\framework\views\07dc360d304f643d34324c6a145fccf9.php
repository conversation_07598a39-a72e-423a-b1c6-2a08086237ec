<div class="modal fade" id="deleteBenefitModal" >
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"><?php echo app('translator')->get('common.delete'); ?> <?php echo app('translator')->get('frontendcms.benefit'); ?> </h4>
                <button type="button" class="close" data-dismiss="modal">
                    <i class="ti-close "></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <h4><?php echo app('translator')->get('common.are_you_sure_to_delete_?'); ?></h4>
                </div>
                <div class="mt-40 d-flex justify-content-between">
                    <button type="button" class="primary-btn tr-bg" data-dismiss="modal"><?php echo app('translator')->get('common.cancel'); ?></button>
                    <form id="benefit_delete_form">
                        <input type="hidden" name="id" id="delete_benefit_id">
                    <button id="delete_benefit_btn" type="submit" class="primary-btn fix-gr-bg"><?php echo e(__('common.delete')); ?></button>
                    </form>
                </div>
            </div>

        </div>
    </div>
</div>
<div class="modal fade" id="deleteWorkModal" >
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"><?php echo app('translator')->get('common.delete'); ?> <?php echo app('translator')->get('frontendCms.how_it_work'); ?> </h4>
                <button type="button" class="close" data-dismiss="modal">
                    <i class="ti-close "></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <h4><?php echo app('translator')->get('common.are_you_sure_to_delete_?'); ?></h4>
                </div>
                <div class="mt-40 d-flex justify-content-between">
                    <button type="button" class="primary-btn tr-bg" data-dismiss="modal"><?php echo app('translator')->get('common.cancel'); ?></button>
                    <form id="work_delete_form">
                        <input type="hidden" name="id" id="delete_work_id">
                        <button id="delete_work_btn" type="submit" class="primary-btn fix-gr-bg"><?php echo e(__('common.delete')); ?></button>
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="deleteFaqModal" >
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"><?php echo app('translator')->get('common.delete'); ?> <?php echo app('translator')->get('frontendcms.faq'); ?> </h4>
                <button type="button" class="close" data-dismiss="modal">
                    <i class="ti-close "></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <h4><?php echo app('translator')->get('common.are_you_sure_to_delete_?'); ?></h4>
                </div>
                <div class="mt-40 d-flex justify-content-between">
                    <button type="button" class="primary-btn tr-bg" data-dismiss="modal"><?php echo app('translator')->get('common.cancel'); ?></button>
                    <form id="faq_delete_form">
                        <input type="hidden" name="id" id="delete_faq_id">
                        <button id="delete_faq_btn" type="submit" class="primary-btn fix-gr-bg"><?php echo e(__('common.delete')); ?></button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div><?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\Modules/FrontendCMS\Resources/views/merchant/components/deletemodal.blade.php ENDPATH**/ ?>