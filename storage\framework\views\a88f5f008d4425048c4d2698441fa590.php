<form enctype="multipart/form-data" id="<?php echo e($form_id); ?>">
    <div class="row">
        <?php if(isModuleActive('FrontendMultiLang')): ?>
            <div class="col-lg-12">
                <ul class="nav nav-tabs justify-content-start mt-sm-md-20 mb-30 grid_gap_5" role="tablist">
                    <?php $__currentLoopData = $LanguageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="nav-item lang_code" data-id="<?php echo e($language->code); ?>">
                            <a class="nav-link anchore_color <?php if(auth()->user()->lang_code == $language->code): ?> active <?php endif; ?>" href="#<?php echo e($faq_modal_tab_id); ?><?php echo e($language->code); ?>" role="tab" data-toggle="tab" aria-selected="<?php if(auth()->user()->lang_code == $language->code): ?> true <?php else: ?> false <?php endif; ?>"><?php echo e($language->native); ?> </a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
                <div class="tab-content">
                    <?php $__currentLoopData = $LanguageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div role="tabpanel" class="tab-pane fade <?php if(auth()->user()->lang_code == $language->code): ?> show active <?php endif; ?>" id="<?php echo e($faq_modal_tab_id); ?><?php echo e($language->code); ?>">
                            <div class="row">                                
                                <div class="col-lg-12 col-md-12 col-sm-12">
                                    <div class="primary_input mb-25">
                                        <label class="primary_input_label" for=""><?php echo e(__('common.title')); ?> <span class="text-danger">*</span></label></label>
                                        <input name="title[<?php echo e($language->code); ?>]" class="primary_input_field title" id="title<?php echo e($language->code); ?>" placeholder="<?php echo e(__('common.title')); ?>" type="text" value="<?php echo e(old('title.'.$language->code)); ?>">
                                    </div>
                                    <span class="text-danger"  id="create_error_title_<?php echo e($language->code); ?>"></span>
                                </div>
                                <div class="col-xl-12">
                                    <div class="primary_input mb-35">
                                        <label class="primary_input_label" for=""><?php echo e(__('common.details')); ?> <span class="text-danger">*</span></label>
                                        <textarea name="description[<?php echo e($language->code); ?>]" placeholder="<?php echo e(__('common.description')); ?>" class="benefit_description" id="description<?php echo e($language->code); ?>"><?php echo e(old('description.'.$language->code)); ?></textarea>
                                    </div>
                                    <span class="text-danger" id="create_error_description_<?php echo e($language->code); ?>"></span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php else: ?>
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="primary_input mb-25">
                    <label class="primary_input_label" for=""><?php echo e(__('common.title')); ?> *</label>
                    <input name="title" class="primary_input_field title" id="title" placeholder="<?php echo e(__('common.title')); ?>" type="text">
                    <span class="text-danger"  id="create_error_title"></span>
                </div>
            </div>
            <div class="col-xl-12">
                <div class="primary_input mb-35">
                    <label class="primary_input_label" for=""><?php echo e(__('common.details')); ?> <span class="text-danger">*</span></label>
                    <textarea name="description" placeholder="<?php echo e(__('common.description')); ?>" class="benefit_description" id="description"></textarea>
                </div>
                <span class="text-danger" id="create_error_description"></span>
            </div>
        <?php endif; ?>
        <div class="col-xl-12">
            <div class="primary_input">
                <label class="primary_input_label" for=""><?php echo e(__('common.status')); ?></label>
                <ul id="theme_nav" class="permission_list sms_list ">
                    <li>
                        <label data-id="bg_option"
                               class="primary_checkbox d-flex mr-12">
                            <input name="status" id="status_active" value="1" checked="true" class="active" type="radio">
                            <span class="checkmark"></span>
                        </label>
                        <p><?php echo e(__('common.active')); ?></p>
                    </li>
                    <li>
                        <label data-id="color_option"
                               class="primary_checkbox d-flex mr-12">
                            <input name="status" value="0" id="status_inactive"  class="de_active"
                                   type="radio">
                            <span class="checkmark"></span>
                        </label>
                        <p><?php echo e(__('common.inactive')); ?></p>
                    </li>
                </ul>
                <span class="text-danger" id="create_error_status"></span>
            </div>
        </div>
        <div class="col-lg-12 text-center">
            <div class="d-flex justify-content-center pt_20">
            <button id="<?php echo e($btn_id); ?>" type="submit" class="primary-btn semi_large2 fix-gr-bg"><i
                        class="ti-check"></i>
                        <?php echo e($button_level_name); ?>

                </button>
            </div>
        </div>

    </div>
</form>
<?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\Modules/FrontendCMS\Resources/views/merchant/faq/form.blade.php ENDPATH**/ ?>