

<?php
$elements = ['datatable', 'nice-select', 'datepicker']
?>
<?php $__env->startPush('styles'); ?>
<?php if(Route::has('_asset.css')): ?>
<link rel="stylesheet" href="<?php echo e(route('_asset.css', ['elements' => $elements])); ?>">
<?php endif; ?>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<?php if(Route::has('_asset.js')): ?>
<script type="text/javascript" src="<?php echo e(route('_asset.js', ['elements' => $elements])); ?>"></script>
<?php else: ?>
<script src="<?php echo e(asset(asset_path('vendor/datatables/buttons.server-side.js'))); ?>"></script>
<?php endif; ?>
<?php
echo $dataTable->scripts();
?>

<?php if (isset($component)) { $__componentOriginal71c6471fa76ce19017edc287b6f4508c = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.backEnd.delete_modal','data' => ['datatable' => 'income-table']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('backEnd.delete_modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['datatable' => 'income-table']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal71c6471fa76ce19017edc287b6f4508c)): ?>
<?php $component = $__componentOriginal71c6471fa76ce19017edc287b6f4508c; ?>
<?php unset($__componentOriginal71c6471fa76ce19017edc287b6f4508c); ?>
<?php endif; ?>

<?php $__env->stopPush(); ?>



<?php $__env->startSection('mainContent'); ?>
<section class="admin-visitor-area up_st_admin_visitor">
    <div class="container-fluid p-0">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="box_header common_table_header">
                    <div class="main-title d-md-flex">
                        <h3 class="mb-0 mr-30 mb_xs_15px mb_sm_20px"><?php echo e(__('income.Incomes')); ?></h3>
                        <?php if(permissionCheck('account.incomes.store')): ?>
                        <ul class="d-flex">
                            <li>
                                <a data-container="income_modal" data-href="<?php echo e(route('account.incomes.create')); ?>"
                                    class="primary-btn radius_30px mr-10 fix-gr-bg btn-modal">
                                    <i class="ti-plus"></i> <?php echo e(__('income.New Income')); ?>

                                </a>
                            </li>
                        </ul>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-xl-12">
                <div class="QA_section QA_section_heading_custom check_box_table">
                    <div class="QA_table ">
                        <!-- table-responsive -->
                        <div class="">
                            <div id="chart_account_list">

                                <?php echo e($dataTable->table([], true)); ?>


                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="modal fade income_modal" id="income_modal">
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('account::layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\Modules/Account\Resources/views/income/index.blade.php ENDPATH**/ ?>