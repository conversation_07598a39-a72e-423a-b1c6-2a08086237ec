<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['id' => 'remote_modal']) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['id' => 'remote_modal']); ?>
<?php foreach (array_filter((['id' => 'remote_modal']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>
<div class="modal animated rotateInDownLeft <?php echo e($id); ?> " id="<?php echo e($id); ?>" tabindex="-1" role="dialog" aria-labelledby="remote_modal_label" aria-hidden="true" data-backdrop="static">
    <?php echo e($slot); ?>

</div>
<?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\resources\views/components/backEnd/modal/container.blade.php ENDPATH**/ ?>