
<div class="modal fade admin-query" id="Recharge_Modal">
    <div class="modal-dialog modal_800px modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"><?php echo e(__('wallet.enter_your_amount_to_recharge')); ?></h4>
                <button type="button" class="close " data-dismiss="modal">
                    <i class="ti-close "></i>
                </button>
            </div>

            <div class="modal-body">
                <form  action="<?php echo e(route('my-wallet.recharge_create')); ?>" method="post" id="recharge_form">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="primary_input mb-25">
                                <label class="primary_input_label" for=""><?php echo e(__('wallet.amount')); ?> <span class="text-danger">*</span></label>
                                <input name="recharge_amount" id="recharge_amount" class="primary_input_field" placeholder="<?php echo e(__('wallet.amount')); ?>" type="number" step="<?php echo e(step_decimal()); ?>" min="1" value="1">
                                <?php $__errorArgs = ['recharge_amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-lg-12 text-center">
                            <div class="d-flex justify-content-center pt_20">
                                <button type="submit" class="primary-btn semi_large2 fix-gr-bg" id="save_button_parent"><i class="ti-check"></i><?php echo e(__('wallet.continue')); ?>

                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\Modules/Wallet\Resources/views/backend/seller/recharge_modal.blade.php ENDPATH**/ ?>