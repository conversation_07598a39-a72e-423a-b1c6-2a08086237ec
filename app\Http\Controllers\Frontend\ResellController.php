<?php

namespace App\Http\Controllers\Frontend;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Redirect;
use Modules\Bkash\Http\Controllers\BkashController;
use Modules\MercadoPago\Http\Controllers\MercadoPagoController;
use Modules\PaymentGateway\Http\Controllers\StripeController;
use Modules\PaymentGateway\Http\Controllers\RazorpayController;
use Modules\PaymentGateway\Http\Controllers\PayPalController;
use Modules\PaymentGateway\Http\Controllers\PaystackController;
use Modules\PaymentGateway\Http\Controllers\PaytmController;
use Modules\PaymentGateway\Http\Controllers\InstamojoController;
use Modules\PaymentGateway\Http\Controllers\BankPaymentController;
use Modules\PaymentGateway\Http\Controllers\MidtransController;
use Modules\PaymentGateway\Http\Controllers\PayUmoneyController;
use Modules\PaymentGateway\Http\Controllers\FlutterwaveController;
use App\Models\DigitalFileDownload;
use App\Models\OrderProductDetail;
use Modules\OrderManage\Repositories\CancelReasonRepository;
use Modules\Shipping\Http\Controllers\OrderSyncWithCarrierController;
use Modules\SslCommerz\Library\SslCommerz\SslCommerzNotification;
use App\Services\OrderService;
use Modules\OrderManage\Repositories\DeliveryProcessRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Brian2694\Toastr\Facades\Toastr;
use App\Traits\OrderPdf;
use App\Traits\Otp;
use App\Traits\SendMail;
use Exception;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Session;
use Modules\CCAvenue\Http\Controllers\CCAvenueController;
use Modules\Clickpay\Http\Controllers\ClickpayController;
use Modules\Setup\Entities\City;
use Modules\Setup\Entities\Country;
use Modules\Setup\Entities\State;
use Modules\PaymentGateway\Http\Controllers\TabbyPaymentController;
use Modules\Seller\Entities\SellerProduct;
use Modules\Seller\Entities\SellerProductSKU;
use Modules\Product\Entities\Product;

class ResellController extends Controller
{
    public function resellProduct(Request $request){
        $products = SellerProduct::where('seller_id', auth()->user()->id)->get();
        return view('frontend.resell.product', compact('products'));
    }

    public function resellProductList(Request $request)
    {
        $query = Product::where('reseller_id', auth()->id())
            ->where('resell_product', 1);

        // Search functionality
        if ($request->filled('search')) {
            $query->where('product_name', 'like', '%' . $request->search . '%');
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Sorting
        switch ($request->get('sort', 'newest')) {
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'price_high':
                $query->orderBy('resell_price', 'desc');
                break;
            case 'price_low':
                $query->orderBy('resell_price', 'asc');
                break;
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        $resellProducts = $query->paginate(10);

        return view(theme('pages.profile.resell_product_list'), compact('resellProducts'));
    }

    public function resellDashboard()
    {
        $userId = auth()->id();

        // Get statistics
        $stats = [
            'total_products' => Product::where('reseller_id', $userId)->where('resell_product', 1)->count(),
            'active_products' => Product::where('reseller_id', $userId)->where('resell_product', 1)->where('status', 1)->count(),
            'total_sales' => 0, // TODO: Implement sales tracking
            'total_earnings' => 0, // TODO: Implement earnings tracking
        ];

        // Get recent products
        $recentProducts = Product::where('reseller_id', $userId)
            ->where('resell_product', 1)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return view(theme('pages.profile.resell_dashboard'), compact('stats', 'recentProducts'));
    }

    public function updatePrice(Request $request, $id)
    {
        $request->validate([
            'new_price' => 'required|numeric|min:0.01'
        ]);

        $product = Product::where('id', $id)
            ->where('reseller_id', auth()->id())
            ->where('resell_product', 1)
            ->firstOrFail();

        $product->resell_price = $request->new_price;
        $product->save();

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Price updated successfully!'
            ]);
        }

        return redirect()->back()->with('success', 'Price updated successfully!');
    }

    public function toggleStatus($id)
    {
        $product = Product::where('id', $id)
            ->where('reseller_id', auth()->id())
            ->where('resell_product', 1)
            ->firstOrFail();

        $product->status = $product->status == 1 ? 0 : 1;
        $product->save();

        return response()->json([
            'success' => true,
            'message' => 'Product status updated successfully!',
            'new_status' => $product->status
        ]);
    }

    public function deleteProduct($id)
    {
        $product = Product::where('id', $id)
            ->where('reseller_id', auth()->id())
            ->where('resell_product', 1)
            ->firstOrFail();

        $product->delete();

        return response()->json([
            'success' => true,
            'message' => 'Product deleted successfully!'
        ]);
    }
}
