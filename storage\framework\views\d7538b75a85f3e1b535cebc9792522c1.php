<?php $__env->startPush('scripts'); ?>
<script>
    (function($){
        "use strict";
        $(document).ready(function() {
            $(document).on('submit', '#formData', function(event) {
                event.preventDefault();
                $('#pre-loader').removeClass('d-none');
                resetValidationErrors();
                $("#mainSubmit").prop('disabled', true);
                $('#mainSubmit').text('<?php echo e(__("common.updating")); ?>');
                var formElement = $(this).serializeArray()
                var formData = new FormData();
                formElement.forEach(element => {
                    formData.append(element.name, element.value);
                });
                $.ajax({
                    url: "<?php echo e(route('frontendcms.merchant-content.update')); ?>",
                    type: "POST",
                    cache: false,
                    contentType: false,
                    processData: false,
                    data: formData,
                    success: function(response) {
                        toastr.success("<?php echo e(__('common.updated_successfully')); ?>","<?php echo e(__('common.success')); ?>")
                        $('#mainSubmit').text('<?php echo e(__("common.update")); ?>');
                        $("#mainSubmit").prop('disabled', false);
                        $('#pre-loader').addClass('d-none');
                    },
                    error: function(response) {
                        if(response.responseJSON.error){
                            toastr.error(response.responseJSON.error ,"<?php echo e(__('common.error')); ?>");
                            $('#pre-loader').addClass('d-none');
                            return false;
                        }
                        toastr.error("<?php echo e(__('common.error_message')); ?>","<?php echo e(__('common.error')); ?>");
                        $("#mainSubmit").prop('disabled', false);
                        $('#mainSubmit').text('<?php echo e(__("common.update")); ?>');
                        showValidationErrors('#formData', response.responseJSON.errors);
                        $('#pre-loader').addClass('d-none');
                    }
                });
            });
            <?php if(isModuleActive('FrontendMultiLang')): ?>
            $(document).on('keyup', '#mainTitle<?php echo e(auth()->user()->lang_code); ?>', function(event){
                processSlug($(this).val(), '#mainSlug<?php echo e(auth()->user()->lang_code); ?>');
            });
            <?php else: ?>
            $(document).on('keyup', '#mainTitle', function(event){
                processSlug($(this).val(), '#mainSlug');
            });
            <?php endif; ?>
            function showValidationErrors(formType, errors) {
                <?php if(isModuleActive('FrontendMultiLang')): ?>
                $(formType + ' #error_mainTitle_<?php echo e(auth()->user()->lang_code); ?>').text(errors['mainTitle.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_subTitle_<?php echo e(auth()->user()->lang_code); ?>').text(errors['subTitle.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_mainDescription_<?php echo e(auth()->user()->lang_code); ?>').text(errors['Maindescription.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_slug_<?php echo e(auth()->user()->lang_code); ?>').text(errors['slug.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_pricing_<?php echo e(auth()->user()->lang_code); ?>').text(errors['pricing.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_benifitTitle_<?php echo e(auth()->user()->lang_code); ?>').text(errors['benifitTitle.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_benifitDescription_<?php echo e(auth()->user()->lang_code); ?>').text(errors['benifitDescription.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_howitworkTitle_<?php echo e(auth()->user()->lang_code); ?>').text(errors['howitworkTitle.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_howitworkDescription_<?php echo e(auth()->user()->lang_code); ?>').text(errors['howitworkDescription.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_pricingTitle_<?php echo e(auth()->user()->lang_code); ?>').text(errors['pricingTitle.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_pricingDescription_<?php echo e(auth()->user()->lang_code); ?>').text(errors['pricingDescription.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_queryTitle_<?php echo e(auth()->user()->lang_code); ?>').text(errors['queryTitle.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_queryDescription_<?php echo e(auth()->user()->lang_code); ?>').text(errors['queryDescription.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_faqTitle_<?php echo e(auth()->user()->lang_code); ?>').text(errors['faqTitle.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_faqDescription_<?php echo e(auth()->user()->lang_code); ?>').text(errors['faqDescription.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_sellerRegistrationTitle_<?php echo e(auth()->user()->lang_code); ?>').text(errors['sellerRegistrationTitle.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #error_sellerRegistrationDescription_<?php echo e(auth()->user()->lang_code); ?>').text(errors['sellerRegistrationDescription.<?php echo e(auth()->user()->lang_code); ?>']);
                <?php else: ?>
                $(formType + ' #error_mainTitle').text(errors.mainTitle);
                $(formType + ' #error_subTitle').text(errors.subTitle);
                $(formType + ' #error_mainDescription').text(errors.Maindescription);
                $(formType + ' #error_slug').text(errors.slug);
                $(formType + ' #error_pricing').text(errors.pricing);
                $(formType + ' #error_benifitTitle').text(errors.benifitTitle);
                $(formType + ' #error_benifitDescription').text(errors.benifitDescription);
                $(formType + ' #error_howitworkTitle').text(errors.howitworkTitle);
                $(formType + ' #error_howitworkDescription').text(errors.howitworkDescription);
                $(formType + ' #error_pricingTitle').text(errors.pricingTitle);
                $(formType + ' #error_pricingDescription').text(errors.pricingDescription);
                $(formType + ' #error_queryTitle').text(errors.queryTitle);
                $(formType + ' #error_queryDescription').text(errors.queryDescription);
                $(formType + ' #error_faqTitle').text(errors.faqTitle);
                $(formType + ' #error_faqDescription').text(errors.faqDescription);
                $(formType + ' #error_sellerRegistrationTitle').text(errors.sellerRegistrationTitle);
                $(formType + ' #error_sellerRegistrationDescription').text(errors.sellerRegistrationDescription);
                <?php endif; ?>
            }
            function resetValidationErrors() {
                <?php if(isModuleActive('FrontendMultiLang')): ?>
                $('#formData' + ' #error_mainTitle_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_subTitle_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_mainDescription_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_slug_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_pricing_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_benifitTitle_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_benifitDescription_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_howitworkTitle_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_howitworkDescription_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_pricingTitle_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_pricingDescription_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_queryTitle_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_queryDescription_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_faqTitle_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_faqDescription_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_sellerRegistrationTitle_<?php echo e(auth()->user()->lang_code); ?>').text('');
                $('#formData' + ' #error_sellerRegistrationDescription_<?php echo e(auth()->user()->lang_code); ?>').text('');
                <?php else: ?>
                $('#formData' + ' #error_mainTitle').text('');
                $('#formData' + ' #error_subTitle').text('');
                $('#formData' + ' #error_mainDescription').text('');
                $('#formData' + ' #error_slug').text('');
                $('#formData' + ' #error_pricing').text('');
                $('#formData' + ' #error_benifitTitle').text('');
                $('#formData' + ' #error_benifitDescription').text('');
                $('#formData' + ' #error_howitworkTitle').text('');
                $('#formData' + ' #error_howitworkDescription').text('');
                $('#formData' + ' #error_pricingTitle').text('');
                $('#formData' + ' #error_pricingDescription').text('');
                $('#formData' + ' #error_queryTitle').text('');
                $('#formData' + ' #error_queryDescription').text('');
                $('#formData' + ' #error_faqTitle').text('');
                $('#formData' + ' #error_faqDescription').text('');
                $('#formData' + ' #error_sellerRegistrationTitle').text('');
                $('#formData' + ' #error_sellerRegistrationDescription').text('');
                <?php endif; ?>
            }
            function showValidationErrorsForBenefit(formType, errors) {
                <?php if(isModuleActive('FrontendMultiLang')): ?>
                $(formType + ' #create_error_title_<?php echo e(auth()->user()->lang_code); ?>').text(errors['title.<?php echo e(auth()->user()->lang_code); ?>']);
                $(formType + ' #create_error_description_<?php echo e(auth()->user()->lang_code); ?>').text(errors['description.<?php echo e(auth()->user()->lang_code); ?>']);
                <?php else: ?>
                $(formType + ' #create_error_title').text(errors.title);
                $(formType + ' #create_error_description').text(errors.description);
                <?php endif; ?>
                $(formType + ' #create_error_slug').text(errors.slug);
                $(formType + ' #create_error_image').text(errors.image);
            }
            function resetForm() {
                $('form')[1].reset();
            }
            <?php if(isModuleActive('FrontendMultiLang')): ?>
                $(document).on('click', '.marchent_content', function(event){
                    var lang = $(this).data('id');
                    $('.default_lang').removeClass('active show');
                    $('#benefitelement'+lang).addClass('active show');
                    $('#howitworkelement'+lang).addClass('active show');
                    $('#pricingelement'+lang).addClass('active show');
                    $('#sellerRegistrationelement'+lang).addClass('active show');
                    $('#faqelement'+lang).addClass('active show');
                    $('#queryelement'+lang).addClass('active show');
                    if (lang == "<?php echo e(auth()->user()->lang_code); ?>") {  
                        $('#default_lang_<?php echo e(auth()->user()->lang_code); ?>').removeClass('d-none');
                    }
                });
                if ("<?php echo e(auth()->user()->lang_code); ?>") {  
                        $('#default_lang_<?php echo e(auth()->user()->lang_code); ?>').removeClass('d-none');
                }
            <?php endif; ?>
        });
    })(jQuery);
</script>
<?php echo $__env->make('frontendcms::merchant.benefit.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('frontendcms::merchant.working_process.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('frontendcms::merchant.faq.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\Modules/FrontendCMS\Resources/views/merchant/components/scripts.blade.php ENDPATH**/ ?>