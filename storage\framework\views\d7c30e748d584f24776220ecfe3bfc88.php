
<?php $__env->startSection('styles'); ?>

<?php $__env->startSection('title'); ?>
    <?php echo e(__('My Resell Products')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="amazy_dashboard_area dashboard_bg section_spacing6">
        <div class="container">
            <div class="row">
                <div class="col-xl-3 col-lg-4">
                    <?php echo $__env->make('frontend.amazy.pages.profile.partials._menu', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div class="col-xl-9 col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="font_20 f_w_700 mb-0"><?php echo e(__('My Resell Products')); ?></h3>
                        </div>
                        <div class="card-body">
                            <?php if(isset($resellProducts) && $resellProducts->count() > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Resell Price</th>
                                                <th>Status</th>
                                                <th>Created Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $resellProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <?php if($product->thumbnail_image_source): ?>
                                                                <img src="<?php echo e(showImage($product->thumbnail_image_source)); ?>"
                                                                    alt="<?php echo e($product->product_name); ?>"
                                                                    class="img-thumbnail me-3"
                                                                    style="width: 50px; height: 50px; object-fit: cover;">
                                                            <?php endif; ?>
                                                            <div>
                                                                <h6 class="mb-0"><?php echo e($product->product_name); ?></h6>
                                                                <?php if($product->resell_description): ?>
                                                                    <small class="text-muted"><?php echo e(\Illuminate\Support\Str::limit($product->resell_description, 50)); ?></small>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <strong class="text-primary"><?php echo e(single_price($product->resell_price)); ?></strong>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-success"><?php echo e(__('Active')); ?></span>
                                                    </td>
                                                    <td><?php echo e($product->created_at->format('M d, Y')); ?></td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                                onclick="viewProduct(<?php echo e($product->id); ?>)">
                                                                <i class="fas fa-eye"></i> <?php echo e(__('View')); ?>

                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                                onclick="deleteProduct(<?php echo e($product->id); ?>)">
                                                                <i class="fas fa-trash"></i> <?php echo e(__('Delete')); ?>

                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>

                                <?php if($resellProducts->lastPage() > 1): ?>
                                    <div class="d-flex justify-content-center mt-4">
                                        <?php echo e($resellProducts->links()); ?>

                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted"><?php echo e(__('No Resell Products Found')); ?></h5>
                                    <p class="text-muted"><?php echo e(__('You haven\'t submitted any products for resale yet.')); ?>

                                    </p>
                                    <a href="<?php echo e(route('frontend.my_purchase_order_list')); ?>" class="btn btn-primary">
                                        <?php echo e(__('Go to My Orders')); ?>

                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            $('[data-toggle="tooltip"]').tooltip();
        });

        function viewProduct(productId) {
            // You can implement a modal or redirect to product view
            window.open('/product/' + productId, '_blank');
        }

        function deleteProduct(productId) {
            if (confirm('Are you sure you want to delete this resell product?')) {
                // You can implement delete functionality here
                $.ajax({
                    url: '/resell-product/delete/' + productId,
                    type: 'DELETE',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>'
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('Error deleting product. Please try again.');
                    }
                });
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend.amazy.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\resources\views/frontend/amazy/pages/profile/resell_product_list.blade.php ENDPATH**/ ?>