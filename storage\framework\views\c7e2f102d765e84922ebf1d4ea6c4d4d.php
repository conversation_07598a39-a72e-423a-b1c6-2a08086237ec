<?php $__env->startSection('title'); ?>
    <?php echo e(__('Resell Dashboard')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="dashboard_part">
    <div class="container">
        <div class="row">
            <div class="col-lg-3">
                <?php echo $__env->make(theme('pages.profile.partials._dashboard_nav'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </div>
            <div class="col-lg-9">
                <div class="dashboard_content_wrapper">
                    <div class="dashboard_content">
                        <div class="personal_info_wrapper">
                            <div class="primary_input_field">
                                <h4><?php echo e(__('Resell Dashboard')); ?></h4>
                            </div>
                        </div>

                        <!-- Statistics Cards -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo e($stats['total_products'] ?? 0); ?></h5>
                                        <p class="card-text">Total Products</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo e($stats['active_products'] ?? 0); ?></h5>
                                        <p class="card-text">Active Products</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo e($stats['total_sales'] ?? 0); ?></h5>
                                        <p class="card-text">Total Sales</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title"><?php echo e(single_price($stats['total_earnings'] ?? 0)); ?></h5>
                                        <p class="card-text">Total Earnings</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Quick Actions</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <a href="<?php echo e(route('frontend.resell_product_list')); ?>" class="btn btn-primary btn-block">
                                                    <i class="fas fa-list"></i> My Resell Products
                                                </a>
                                            </div>
                                            <div class="col-md-4">
                                                <a href="<?php echo e(route('frontend.my_purchase_order_list')); ?>" class="btn btn-success btn-block">
                                                    <i class="fas fa-plus"></i> Add New Product
                                                </a>
                                            </div>
                                            <div class="col-md-4">
                                                <a href="<?php echo e(route('frontend.my_purchase_order_list')); ?>" class="btn btn-info btn-block">
                                                    <i class="fas fa-chart-line"></i> Sales Report
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Products -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between">
                                        <h5>Recent Resell Products</h5>
                                        <a href="<?php echo e(route('frontend.resell_product_list')); ?>" class="btn btn-sm btn-outline-primary">View All</a>
                                    </div>
                                    <div class="card-body">
                                        <?php if(isset($recentProducts) && $recentProducts->count() > 0): ?>
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Product</th>
                                                            <th>Price</th>
                                                            <th>Status</th>
                                                            <th>Created</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php $__currentLoopData = $recentProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <tr>
                                                                <td>
                                                                    <div class="d-flex align-items-center">
                                                                        <img src="<?php echo e(showImage($product->thumbnail_image_source)); ?>" 
                                                                             alt="<?php echo e($product->product_name); ?>" 
                                                                             class="img-thumbnail me-2" 
                                                                             style="width: 50px; height: 50px;">
                                                                        <div>
                                                                            <strong><?php echo e(textLimit($product->product_name, 30)); ?></strong>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td><?php echo e(single_price($product->resell_price)); ?></td>
                                                                <td>
                                                                    <?php if($product->status == 1): ?>
                                                                        <span class="badge bg-success">Active</span>
                                                                    <?php else: ?>
                                                                        <span class="badge bg-danger">Inactive</span>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td><?php echo e($product->created_at->format('M d, Y')); ?></td>
                                                                <td>
                                                                    <div class="btn-group btn-group-sm">
                                                                        <a href="<?php echo e(route('frontend.product.details', $product->slug)); ?>"
                                                                           class="btn btn-outline-primary btn-sm"
                                                                           target="_blank">
                                                                            <i class="fas fa-eye"></i>
                                                                        </a>
                                                                        <button class="btn btn-outline-warning btn-sm" 
                                                                                onclick="editPrice(<?php echo e($product->id); ?>, <?php echo e($product->resell_price); ?>)">
                                                                            <i class="fas fa-edit"></i>
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-center py-4">
                                                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                                <h5>No Resell Products Yet</h5>
                                                <p class="text-muted">Start by purchasing products and marking them for resale.</p>
                                                <a href="<?php echo e(route('frontend.my_purchase_order_list')); ?>" class="btn btn-primary">
                                                    View Your Orders
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Price Modal -->
<div class="modal fade" id="editPriceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Resell Price</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editPriceForm" method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="new_price" class="form-label">New Price</label>
                        <input type="number" class="form-control" id="new_price" name="new_price" step="0.01" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Price</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function editPrice(productId, currentPrice) {
    document.getElementById('new_price').value = currentPrice;
    document.getElementById('editPriceForm').action = '/resell-product/' + productId + '/update-price';
    new bootstrap.Modal(document.getElementById('editPriceModal')).show();
}

document.getElementById('editPriceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const url = this.action;
    
    fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating price: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the price.');
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend.amazy.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\resources\views/frontend/amazy/pages/profile/resell_dashboard.blade.php ENDPATH**/ ?>