<?php $__env->startSection('styles'); ?>

<link rel="stylesheet" href="<?php echo e(asset(asset_path('modules/ordermanage/css/my_sale_details.css'))); ?>" />

<?php $__env->stopSection(); ?>
<?php $__env->startSection('mainContent'); ?>
    <section class="admin-visitor-area up_st_admin_visitor">
        <div class="container-fluid p-0">
            <div class="row justify-content-center">
                <div class="col-lg-12 col-md-12 col-sm-12">
                    <div class="box_header common_table_header">
                        <div class="main-title d-md-flex">
                            <h3 class="mb-0 mr-30 mb_xs_15px mb_sm_20px"><?php echo e(getNumberTranslate($order->order_number)); ?></h3>
                            <?php if(permissionCheck('order_manage.print_order_details')): ?>
                                <ul class="d-flex float-right">
                                    <li><a href="<?php echo e(route('my_order_manage.print_order_details', $order->id)); ?>" target="_blank"
                                       class="primary-btn fix-gr-bg radius_30px mr-10"><?php echo e(__('order.print')); ?></a>
                                    </li>
                                </ul>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-lg-8 student-details">
                    <div class="white_box_50px box_shadow_white" id="printableArea">
                        <div class="row pb-30 border-bottom">
                            <div class="col-md-6 col-lg-6">
                                <div class="logo_div">
                                    <img src="<?php echo e(showImage(app('general_setting')->logo)); ?>" alt="">
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-6 text-right">
                                <h4><?php echo e(getNumberTranslate($order->order_number)); ?></h4>
                            </div>
                        </div>
                        <div class="row mt-30">
                            <div class="col-md-6 col-lg-6">
                                <table class="table-borderless clone_line_table">
                                    <tr>
                                        <td><strong><?php echo e(__('defaultTheme.billing_info')); ?></strong></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.name')); ?></td>
                                        <td>: <?php echo e(($order->customer_id) ? @$order->address->billing_name : $order->guest_info->billing_name); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.email')); ?></td>
                                        <td><a class="link_color" href="mailto:<?php echo e(($order->customer_id) ? @$order->address->billing_email : $order->guest_info->billing_email); ?>">: <?php echo e(($order->customer_id) ? @$order->address->billing_email : $order->guest_info->billing_email); ?></a></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.phone')); ?></td>
                                        <td>: <?php echo e(getNumberTranslate(($order->customer_id) ? @$order->address->billing_phone : @$order->guest_info->billing_phone)); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.address')); ?></td>
                                        <td>: <?php echo e(($order->customer_id) ? @$order->address->billing_address : @$order->guest_info->billing_address); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.city')); ?></td>
                                        <td>: <?php echo e(($order->customer_id) ? @$order->address->getBillingCity->name : @$order->guest_info->getBillingCity->name); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.state')); ?></td>
                                        <td>: <?php echo e(($order->customer_id) ? @$order->address->getBillingState->name : @$order->guest_info->getBillingState->name); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.country')); ?></td>
                                        <td>: <?php echo e(($order->customer_id) ? @$order->address->getBillingCity->name : @$order->guest_info->getBillingCountry->name); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.postcode')); ?></td>
                                        <td>: <?php echo e(getNumberTranslate(($order->customer_id) ? @$order->address->billing_postcode : @$order->guest_info->billing_post_code)); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6 col-lg-6">
                                <table class="table-borderless clone_line_table">
                                    <tr>
                                        <td><strong><?php echo e(__('defaultTheme.seller_info')); ?></strong></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.name')); ?></td>
                                        <td>:  <?php echo e(app('general_setting')->company_name); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.phone')); ?></td>
                                        <td>:  <a class="link_color" href="tel:<?php echo e(getNumberTranslate(app('general_setting')->phone)); ?>"><?php echo e(getNumberTranslate(app('general_setting')->phone)); ?></a></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.email')); ?></td>
                                        <td>:  <a class="link_color" href="mailto:<?php echo e(app('general_setting')->email); ?>"><?php echo e(app('general_setting')->email); ?></a></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('order.website')); ?></td>
                                        <td>
                                            <?php if(getParentSeller()->slug): ?>
                                                <a href="#">:  <?php echo e(route('frontend.seller',getParentSeller()->slug)); ?></a>
                                            <?php elseif(!is_null(app('general_setting')->website_url)): ?>
                                                <a href="#">:  <?php echo e(app('general_setting')->website_url); ?></a>
                                            <?php else: ?>
                                                <a href="#">:  <?php echo e(route('frontend.seller',base64_encode(getParentSellerId()))); ?></a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="row mt-30">
                            <div class="col-md-6 col-lg-6">
                                <table class="table-borderless clone_line_table">
                                    <tr>
                                        <td><strong><?php echo e(__('defaultTheme.shipping_info')); ?></strong></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.name')); ?></td>
                                        <td>: <?php echo e(($order->customer_id) ? @$order->address->shipping_name : @$order->guest_info->shipping_name); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.email')); ?></td>
                                        <td><a class="link_color" href="mailto:<?php echo e(($order->customer_id) ? @$order->address->shipping_email : @$order->guest_info->shipping_email); ?>">: <?php echo e(($order->customer_id) ? @$order->address->shipping_email : @$order->guest_info->shipping_email); ?></a></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.phone')); ?></td>
                                        <td>: <?php echo e(getNumberTranslate(($order->customer_id) ? @$order->address->shipping_phone : @$order->guest_info->shipping_phone)); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.address')); ?></td>
                                        <td>: <?php echo e(($order->customer_id) ? @$order->address->shipping_address : @$order->guest_info->shipping_address); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.city')); ?></td>
                                        <td>: <?php echo e(($order->customer_id) ? @$order->address->getShippingCity->name : @$order->guest_info->getShippingCity->name); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.state')); ?></td>
                                        <td>: <?php echo e(($order->customer_id) ? @$order->address->getShippingState->name : @$order->guest_info->getShippingState->name); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.country')); ?></td>
                                        <td>: <?php echo e(($order->customer_id) ? @$order->address->getShippingCountry->name : @$order->guest_info->getShippingCountry->name); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.postcode')); ?></td>
                                        <td>: <?php echo e(getNumberTranslate(($order->customer_id) ? @$order->address->shipping_postcode : @$order->guest_info->shipping_post_code)); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <?php
                                $seller_id = getParentSellerId();
                                $order_packages = $order->packages->where('seller_id', $seller_id)->where('package_code', $package->package_code)->first();
                                $total_gst = 0;
                            ?>
                            <?php if(file_exists(base_path().'/Modules/GST/') && (app('gst_config')['enable_gst'] == "gst" || app('gst_config')['enable_gst'] == "flat_tax")): ?>
                                <?php $__currentLoopData = $order_packages->gst_taxes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $gst_tax): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $total_gst += $gst_tax->amount;
                                    ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                            <div class="col-md-6 col-lg-6">
                                <table class="table-borderless clone_line_table">
                                    <tr>
                                        <td><strong><?php echo e(__('defaultTheme.payment_info')); ?></strong></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.payment_method')); ?></td>
                                        <td>: <?php echo e($order->GatewayName); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.amount')); ?></td>
                                        <td>: <?php echo e(single_price($order_packages->products->sum('total_price') + $order_packages->shipping_cost + $order_packages->tax_amount + $total_gst)); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('order.txn_id')); ?></td>
                                        <td>: <?php echo e(@$order->order_payment->txn_id); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('common.date')); ?></td>
                                        <td>: <?php echo e(dateConvert(@$order->order_payment->created_at)); ?></td>
                                    </tr>
                                    <tr>
                                        <td><?php echo e(__('defaultTheme.payment_status')); ?></td>
                                        <td>:
                                            <?php if($order->is_paid == 1): ?>
                                                <span><?php echo e(__('common.paid')); ?></span>
                                            <?php else: ?>
                                                <span><?php echo e(__('common.pending')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>

                                <?php if(isModuleActive('Affiliate')): ?>
                                    <?php if($order->affiliateUser): ?>
                                        <table class="table-borderless clone_line_table">
                                            <tr>
                                                <td><strong><?php echo e(__('affiliate.affiliate_user')); ?></strong></td>
                                            </tr>
                                            <tr>
                                                <td><?php echo e(__('common.name')); ?></td>
                                                <td>: <a class="link_color" href=""><?php echo e(@$order->affiliateUser->user->first_name); ?></a></td>
                                            </tr>
                                            <tr>
                                                <td><?php echo e(__('common.email')); ?></td>
                                                <td>: <?php echo e(@$order->affiliateUser->user->email); ?></td>
                                            </tr>
                                            <tr>
                                                <td><?php echo e(__('common.phone')); ?></td>
                                                <td>: <?php echo e(getNumberTranslate(@$order->affiliateUser->user->phone)); ?></td>
                                            </tr>
                                        </table>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>

                        </div>

                        <div class="row mt-30">
                            <div class="col-12 mt-30">
                                <?php if($order_packages->is_cancelled == 1): ?>
                                    <div class="primary_input mb-25">
                                        <label class="primary_input_label red" for="">
                                            <?php echo e(__('defaultTheme.order_cancelled')); ?> - (<?php echo e(getNumberTranslate($order_packages->package_code)); ?>)
                                        </label>
                                    </div>

                                    <div class="primary_input mb-25">
                                        <label class="primary_input_label sub-title" for="">
                                            <?php echo e(@$order_packages->cancel_reason->name); ?>

                                        </label>
                                    </div>
                                <?php endif; ?>
                                <div class="box_header common_table_header">
                                    <h3 class="mb-0 mr-30 mb_xs_15px mb_sm_20px"><?php echo e(__('common.package')); ?>: <?php echo e(getNumberTranslate($order_packages->package_code)); ?></h3>
                                    <ul class="d-flex float-right">
                                        <li> <strong><?php echo e(__('shipping.shipping_method')); ?> : <?php echo e($order_packages->shipping->method_name); ?></strong></li>
                                    </ul>
                                </div>

                                <div class="QA_section QA_section_heading_custom check_box_table">
                                    <div class="QA_table ">
                                        <!-- table-responsive -->
                                        <div class="table-responsive">
                                            <table class="table">
                                                <thead>
                                                    <tr>
                                                        <th scope="col"><?php echo e(__('common.sl')); ?></th>
                                                        <th scope="col"><?php echo e(__('common.image')); ?></th>
                                                        <th scope="col"><?php echo e(__('common.name')); ?></th>
                                                        <th scope="col"><?php echo e(__('common.qty')); ?></th>
                                                        <th scope="col"><?php echo e(__('common.price')); ?></th>
                                                        <th scope="col"><?php echo e(__('common.tax')); ?></th>
                                                        <th scope="col"><?php echo e(__('common.total')); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php $__currentLoopData = $order_packages->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $package_product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <tr>
                                                            <td><?php echo e(getNumberTranslate($key + 1)); ?></td>
                                                            <td>
                                                                <div class="product_img_div">
                                                                    <?php if($package_product->type == "gift_card"): ?>
                                                                        <img src="<?php echo e(showImage(@$package_product->giftCard->thumbnail_image)); ?>"
                                                                             alt="#">
                                                                    <?php else: ?>
                                                                        <?php if(@$package_product->seller_product_sku->sku->product->product_type == 1): ?>
                                                                            <img src="<?php echo e(showImage(@$package_product->seller_product_sku->product->thum_img??@$package_product->seller_product_sku->sku->product->thumbnail_image_source)); ?>"
                                                                                 alt="#">
                                                                        <?php else: ?>
                                                                            <img src="<?php echo e(showImage(@$package_product->seller_product_sku->sku->variant_image?@$package_product->seller_product_sku->sku->variant_image:@$package_product->seller_product_sku->product->thum_img??@$package_product->seller_product_sku->product->product->thumbnail_image_source)); ?>"
                                                                                 alt="#">
                                                                        <?php endif; ?>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </td>
                                                            <td class="text-nowrap">
                                                                <?php if($package_product->type == "gift_card"): ?>
                                                                    <?php echo e(@$package_product->giftCard->name); ?> <br>
                                                                    <a class="green gift_card_div pointer" data-gift-card-id='<?php echo e($package_product->giftCard->id); ?>' data-qty='<?php echo e($package_product->qty); ?>' data-customer-mail='<?php echo e(($order->customer_id) ? $order->shipping_address->customer_email : $order->guest_info->shipping_email); ?>' data-order-id='<?php echo e($order->id); ?>'><i class="ti-email mr-1 green"></i>
                                                                        <?php echo e(($order->gift_card_uses->where('gift_card_id', $package_product->giftCard->id)->first() != null && $order->gift_card_uses->where('gift_card_id', $package_product->giftCard->id)->first()->is_mail_sent) ? "__('order.sent_already')" : "__('order.send_code_now')"); ?>

                                                                    </a>
                                                                <?php else: ?>
                                                                    <?php echo e(@$package_product->seller_product_sku->sku->product->product_name); ?>


                                                                    <?php if(@$package_product->seller_product_sku->product->product->is_physical == 0 && @$package_product->seller_product_sku->sku->digital_file): ?>
                                                                        <br><a class="green is_digital_div pointer" data-customer-id='<?php echo e($order->customer_id); ?>' data-product-sku-id='<?php echo e(@$package_product->seller_product_sku->product_sku_id); ?>' data-seller-sku-id='<?php echo e(@$package_product->seller_product_sku->id); ?>' data-seller-id='<?php echo e($order_packages->seller_id); ?>' data-package-id='<?php echo e($order_packages->id); ?>' data-qty='<?php echo e($package_product->qty); ?>' data-customer-mail='<?php echo e(($order->customer_id) ? @$order->address->shipping_email : @$order->guest_info->shipping_email); ?>' data-order-id='<?php echo e($order->id); ?>'><i class="ti-email mr-1 green"></i>
                                                                           <?php echo e(__('common.send_link_mail')); ?>

                                                                        </a>
                                                                    <?php endif; ?>
                                                                <?php endif; ?>
                                                            </td>
                                                            <?php if($package_product->type == "gift_card"): ?>
                                                                <td><?php echo e(__('common.qty')); ?>: <?php echo e(getNumberTranslate($package_product->qty)); ?></td>
                                                            <?php else: ?>
                                                                <?php if(@$package_product->seller_product_sku->sku->product->product_type == 2): ?>
                                                                    <td class="text-nowrap">
                                                                        <?php echo e(__('common.qty')); ?>: <?php echo e(getNumberTranslate($package_product->qty)); ?>

                                                                        <br>
                                                                        <?php
                                                                            $countCombinatiion = count(@$package_product->seller_product_sku->product_variations);
                                                                        ?>
                                                                        <?php $__currentLoopData = @$package_product->seller_product_sku->product_variations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $combination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <?php if($combination->attribute->id == 1): ?>
                                                                                <div class="box_grid ">
                                                                                    <span><?php echo e($combination->attribute->name); ?>:</span><span class='box variant_color' style="background-color:<?php echo e($combination->attribute_value->value); ?>"></span>
                                                                                </div>
                                                                            <?php else: ?>
                                                                                <?php echo e($combination->attribute->name); ?>:
                                                                                <?php echo e($combination->attribute_value->value); ?>

                                                                            <?php endif; ?>
                                                                            <?php if($countCombinatiion > $key + 1): ?>
                                                                                <br>
                                                                            <?php endif; ?>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </td>
                                                                <?php else: ?>
                                                                    <td><?php echo e(__('common.qty')); ?>: <?php echo e(getNumberTranslate($package_product->qty)); ?></td>
                                                                <?php endif; ?>
                                                            <?php endif; ?>

                                                            <td class="text-nowrap"><?php echo e(single_price($package_product->price)); ?></td>
                                                            <td class="text-nowrap"><?php echo e(single_price($package_product->tax_amount)); ?></td>
                                                            <td class="text-nowrap"><?php echo e(single_price($package_product->price * $package_product->qty + $package_product->tax_amount)); ?></td>
                                                        </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </tbody>

                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-30">
                            <div class="col-md-6">
                                <table class="table-borderless clone_line_table">
                                    <tr>
                                        <td><strong><?php echo e(__('order.order_info')); ?></strong></td>
                                    </tr>
                                    <tr>
                                        <td class="info_tbl"><?php echo e(__('order.is_paid')); ?></td>
                                        <td>: <?php echo e($order->is_paid == 1 ? __('common.yes') : __('common.no')); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="info_tbl"><?php echo e(__('order.is_cancelled')); ?></td>
                                        <td>: <?php echo e($order->is_cancelled == 1 ? __('common.yes') : __('common.no')); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table-borderless clone_line_table ml-md-auto mt-md-0 mt-2">
                                    <tr>
                                        <td><strong><?php echo e(__('common.order_summary')); ?></strong></td>
                                    </tr>
                                    <tr>
                                        <td class="info_tbl"><?php echo e(__('order.subtotal')); ?></td>
                                        <td>: <?php echo e(single_price($order_packages->products->sum('total_price'))); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="info_tbl"><?php echo e(__('common.shipping_charge')); ?></td>
                                        <td>: <?php echo e(single_price($order_packages->shipping_cost)); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="info_tbl"><?php echo e(__('common.tax')); ?>/<?php echo e(__('gst.gst')); ?></td>
                                        <td>: <?php echo e(single_price($order_packages->tax_amount)); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="info_tbl"><?php echo e(__('order.grand_total')); ?></td>
                                        <td>: <?php echo e(single_price($order_packages->products->sum('total_price') + $order_packages->shipping_cost + $order_packages->tax_amount)); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 student-details">
                    <?php if($order->is_cancelled != 1 && $order_packages->is_cancelled != 1): ?>
                        <?php if(permissionCheck('order_manage.update_delivery_status')): ?>
                            <form action="<?php echo e(route('order_manage.update_delivery_status', $order_packages->id)); ?>" method="post">
                                <?php echo csrf_field(); ?>
                                <div class="row white_box p-25 box_shadow_white mr-0 ml-0">
                                    <?php if($order_packages->order->is_confirmed == 0): ?>
                                    <div class="col-lg-12">
                                        <div class="primary_input">
                                            <label class="primary_selectlabel alert alert-warning">
                                                <?php echo e(__('order.status_is_changable_after_confirmed_the_order')); ?>

                                            </label>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    <div class="col-lg-12 p-0">
                                        <div class="primary_input mb-25">
                                            <label class="primary_input_label" for=""> <strong><?php echo e(__('order.delivery_status')); ?></strong></label>
                                            <select class="primary_select mb-25" name="delivery_status" id="delivery_status" >
                                                <?php $__currentLoopData = $processes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $process): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($process->id); ?>" <?php if($order_packages->delivery_status == $process->id): ?> selected <?php endif; ?>><?php echo e($process->name); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-12 p-0">
                                        <div class="primary_input mb-25">
                                            <label class="primary_input_label" for=""> <strong><?php echo e(__('order.note')); ?></strong> </label>
                                            <textarea class="primary_textarea height_112 address" placeholder="<?php echo e(__('order.note')); ?>" name="note" spellcheck="false"></textarea>
                                        </div>
                                    </div>
                                    <div class="col-lg-12 p-0 text-center">
                                        <button class="primary_btn_2"><i class="ti-check"></i><?php echo e(__('common.update')); ?>

                                        </button>
                                    </div>
                                </div>
                            </form>
                        <?php endif; ?>

                        <div class="row mt-2 mr-0 ml-0 white_box p-25 box_shadow_white">
                            <div class="col-lg-12 p-0">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <tr>
                                            <th scope="col"><?php echo e(__('common.state')); ?></th>
                                            <th scope="col"><?php echo e(__('common.date')); ?></th>
                                            <th scope="col"><?php echo e(__('common.note')); ?></th>
                                            <th scope="col"><?php echo e(__('common.updated_by')); ?></th>
                                        </tr>
                                        <?php $__currentLoopData = $order_packages->delivery_states; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $delivery_state): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e(dateConvert(@$delivery_state->delivery_process->created_at)); ?></td>
                                                <td><?php echo e($delivery_state->created_at); ?></td>
                                                <td><?php echo e(@$delivery_state->note); ?></td>
                                                <td><?php echo e(@$delivery_state->creator->first_name); ?></td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </table>
                                </div>
                            </div>
                        </div>

                    <?php endif; ?>

                    <?php if($order->note != null): ?>
                        <div class="row white_box p-25 ml-0 mr-0 box_shadow_white mt-20">
                            <div class="description_box">
                                <h4 class="f_s_14 f_w_500 mb_10"><?php echo e(__('common.order')); ?> <?php echo e(__('common.note')); ?>:</h4>
                                <p class="f_w_400">
                                    <?php echo e($order->note); ?>

                                </p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush("scripts"); ?>
    <script type="text/javascript">

        (function($){
            "use strict";
            $(document).ready(function(){

                $(document).on('click', '.is_digital_div', function(){
                    var customer_id = $(this).attr("data-customer-id");
                    var seller_id = $(this).attr("data-seller-id");
                    var order_id = $(this).attr("data-order-id");
                    var package_id = $(this).attr("data-package-id");
                    var seller_product_sku_id = $(this).attr("data-seller-sku-id");
                    var product_sku_id = $(this).attr("data-product-sku-id");
                    var mail = $(this).attr("data-customer-mail");
                    var qty = $(this).attr("data-qty");
                    $(this).text('Sending.....');
                    var _this = this;
                    $.post('<?php echo e(route('send_digital_file_access_to_customer')); ?>', {_token:'<?php echo e(csrf_token()); ?>', customer_id:customer_id, seller_id:seller_id, order_id:order_id, package_id:package_id, seller_product_sku_id:seller_product_sku_id, product_sku_id:product_sku_id, mail:mail, qty:qty}, function(data){
                        if (data == "true" || data == 1) {
                            toastr.success("<?php echo e(__('common.mail_has_been_sent_successful')); ?>","<?php echo e(__('common.success')); ?>")
                            $(_this).text('Sent')
                        }else {
                            toastr.error("<?php echo e(__('common.error_message')); ?>","<?php echo e(__('common.error')); ?>");

                            $(_this).text("<?php echo e(__('order.send_code_now')); ?>")
                        }
                    });
                });
                $(document).on('click','.gift_card_div', function(){
                    var gift_card_id = $(this).attr("data-gift-card-id");
                    var order_id = $(this).attr("data-order-id");
                    var mail = $(this).attr("data-customer-mail");
                    var qty = $(this).attr("data-qty");
                    $(this).text('Sending.....');
                    var _this = this;
                    $.post('<?php echo e(route('send_gift_card_code_to_customer')); ?>', {_token:'<?php echo e(csrf_token()); ?>', order_id:order_id, mail:mail, gift_card_id:gift_card_id, qty:qty}, function(data){

                        if (data == "true" || data == 1) {

                            toastr.success("<?php echo e(__('common.mail_has_been_sent_successful')); ?>","<?php echo e(__('common.success')); ?>")
                            $(_this).text('Sent')
                        }else {
                            toastr.error("<?php echo e(__('common.error_message')); ?>","<?php echo e(__('common.error')); ?>");
                            $(_this).text("<?php echo e(__('order.send_code_now')); ?>")
                        }

                    }).fail(function(response) {
                        if(response.responseJSON.msg){
                            toastr.error(response.responseJSON.msg ,"<?php echo e(__('common.error')); ?>");
                            $('#pre-loader').addClass('d-none');
                            $(_this).text('Already Used')
                            return false;
                        }
                    });
                });

                function printDiv(divName) {
                    var printContents = document.getElementById(divName).innerHTML;
                    var originalContents = document.body.innerHTML;
                    document.body.innerHTML = printContents;
                    window.print();
                    document.body.innerHTML = originalContents;
                    setTimeout(function () {
                        window.location.reload();
                    }, 15000);
                }
            });
        })(jQuery);


    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backEnd.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\Modules/OrderManage\Resources/views/order_manage/my_sale_details.blade.php ENDPATH**/ ?>