<?php $__env->startPush('scripts'); ?>
    <script>

        (function($){
            "use strict";
            $(document).ready(function(){

                approveReviewDatatable();

                $(document).on('submit','#ReplyForm', function(event){
                    event.preventDefault();
                    $('#pre-loader').removeClass('d-none');
                    $('#reply_modal').modal('hide');
                    $('#error_review').text('')

                    var formElement = $(this).serializeArray()
                    var formData = new FormData();
                    formElement.forEach(element => {
                        formData.append(element.name, element.value);
                    });

                    formData.append('_token', "<?php echo e(csrf_token()); ?>");
                    $.ajax({
                        url: "<?php echo e(route('seller.product-reviews.reply.store')); ?>",
                        type: "POST",
                        cache: false,
                        contentType: false,
                        processData: false,
                        data: formData,
                        success: function(response) {
                            reloadWithData(response);
                            toastr.success("<?php echo e(__('review.replied_successfully')); ?>","<?php echo e(__('common.success')); ?>");
                            $('#pre-loader').addClass('d-none');
                        },
                        error: function(response) {
                            if(response.responseJSON.error){
                                toastr.error(response.responseJSON.error ,"<?php echo e(__('common.error')); ?>");
                                $('#pre-loader').addClass('d-none');
                                return false;
                            }
                            $('#pre-loader').addClass('d-none');
                            toastr.error('<?php echo e(__("common.error_message")); ?>','<?php echo e(__("common.error")); ?>');
                            $('#error_review').text(response.responseJSON.errors.review)
                        }
                    });
                });

                $(document).on('click', '.reply_btn', function(event){
                    event.preventDefault();
                    let id = $(this).data('id');
                    ProductReviewReply(id);
                });

                function reloadWithData(response){
                    $('#item_table').empty();
                    $('#item_table').html(response.TableData);
                    approveReviewDatatable();
                }

                function approveReviewDatatable(){
                    var url = "<?php echo e(route('seller.product-reviews.get-data')); ?>";
                    $('#approveReviewTable').DataTable({
                        processing: true,
                        serverSide: true,
                        stateSave: true,
                        "ajax": ( {
                            url: url
                        }),
                        "initComplete":function(json){

                        },
                        columns: [
                            { data: 'DT_RowIndex', name: 'id',render:function(data){
                                return numbertrans(data)
                            }},
                            { data: 'rating', name: 'rating' },
                            { data: 'customer_feedback', name: 'customer_feedback' },
                            { data: 'customer_time', name: 'customer_time' },
                            { data: 'reply', name: 'reply' }

                        ],

                        bLengthChange: false,
                        "bDestroy": true,
                        language: {
                            search: "<i class='ti-search'></i>",
                            searchPlaceholder: trans('common.quick_search'),
                            paginate: {
                                next: "<i class='ti-arrow-right'></i>",
                                previous: "<i class='ti-arrow-left'></i>"
                            }
                        },
                        dom: 'Bfrtip',
                        buttons: [{
                                extend: 'copyHtml5',
                                text: '<i class="fa fa-files-o"></i>',
                                title: $("#header_title").text(),
                                titleAttr: 'Copy',
                                exportOptions: {
                                    columns: ':visible',
                                    columns: ':not(:last-child)',
                                }
                            },
                            {
                                extend: 'excelHtml5',
                                text: '<i class="fa fa-file-excel-o"></i>',
                                titleAttr: 'Excel',
                                title: $("#header_title").text(),
                                margin: [10, 10, 10, 0],
                                exportOptions: {
                                    columns: ':visible',
                                    columns: ':not(:last-child)',
                                },

                            },
                            {
                                extend: 'csvHtml5',
                                text: '<i class="fa fa-file-text-o"></i>',
                                titleAttr: 'CSV',
                                exportOptions: {
                                    columns: ':visible',
                                    columns: ':not(:last-child)',
                                }
                            },
                            {
                                extend: 'pdfHtml5',
                                text: '<i class="fa fa-file-pdf-o"></i>',
                                title: $("#header_title").text(),
                                titleAttr: 'PDF',
                                exportOptions: {
                                    columns: ':visible',
                                    columns: ':not(:last-child)',
                                },
                                pageSize: 'A4',
                                margin: [0, 0, 0, 0],
                                alignment: 'center',
                                header: true,

                            },
                            {
                                extend: 'print',
                                text: '<i class="fa fa-print"></i>',
                                titleAttr: 'Print',
                                title: $("#header_title").text(),
                                exportOptions: {
                                    columns: ':not(:last-child)',
                                }
                            },
                            {
                                extend: 'colvis',
                                text: '<i class="fa fa-columns"></i>',
                                postfixButtons: ['colvisRestore']
                            }
                        ],
                        columnDefs: [{
                            visible: false
                        }],
                        responsive: true,
                    });
                }

                function ProductReviewReply(id){
                    $('#pre-loader').removeClass('d-none');
                    let base_url = $('#url').val();
                    let url = base_url + '/seller/product-reviews/reply?id=' + id;
                    $.get(url, function(data) {
                        $('#replyModalDiv').empty();
                        $('#replyModalDiv').html(data);
                        $('#reply_modal').modal('show');
                        $('#pre-loader').addClass('d-none');
                    });
                }

            });
        })(jQuery);

    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\Modules/Seller\Resources/views/product_reviews/components/scripts.blade.php ENDPATH**/ ?>