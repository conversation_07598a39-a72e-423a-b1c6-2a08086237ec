1753121788O:30:"hisorange\BrowserDetect\Result":32:{s:12:" * userAgent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36";s:11:" * isMobile";b:0;s:11:" * isTablet";b:0;s:12:" * isDesktop";b:1;s:8:" * isBot";b:0;s:11:" * isChrome";b:1;s:12:" * isFirefox";b:0;s:10:" * isOpera";b:0;s:11:" * isSafari";b:0;s:9:" * isEdge";b:0;s:10:" * isInApp";b:0;s:7:" * isIE";b:0;s:14:" * browserName";s:10:"Chrome 138";s:16:" * browserFamily";s:6:"Chrome";s:17:" * browserVersion";s:3:"138";s:22:" * browserVersionMajor";i:138;s:22:" * browserVersionMinor";i:0;s:22:" * browserVersionPatch";i:0;s:16:" * browserEngine";s:5:"Blink";s:15:" * platformName";s:10:"Windows 10";s:17:" * platformFamily";s:7:"Windows";s:18:" * platformVersion";s:2:"10";s:23:" * platformVersionMajor";i:10;s:23:" * platformVersionMinor";i:0;s:23:" * platformVersionPatch";i:0;s:12:" * isWindows";b:1;s:10:" * isLinux";b:0;s:8:" * isMac";b:0;s:12:" * isAndroid";b:0;s:15:" * deviceFamily";s:7:"Unknown";s:14:" * deviceModel";s:0:"";s:14:" * mobileGrade";s:0:"";}