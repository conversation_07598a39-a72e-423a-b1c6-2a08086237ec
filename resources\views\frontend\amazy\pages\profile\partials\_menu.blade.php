<div class="dashboard_sidebar_wized border-0">
    <div class="card mb_30 border-0 rounded-3">
        <div class="customer-profile bg-white p-3 rounded-3">
            <div class="customer-info d-flex align-items-center flex-wrap mb-3 rounded-3">
                <div class="customer-info-img">
                    <img id="customerMiniImage" src="{{auth()->user()->avatar?showImage(auth()->user()->avatar):showImage('frontend/default/img/avatar.jpg')}}" alt="{{auth()->user()->first_name}} {{auth()->user()->last_name}}" title="{{auth()->user()->first_name}} {{auth()->user()->last_name}}">                </div>
                <div class="customer-info-content d-flex align-items-center flex-wrap">
                    <div>
                        <p class="text-white">{{auth()->user()->first_name}} {{auth()->user()->last_name}}</p>
                        @if(auth()->user()->email)
                            <span class="email_text font_14 f_w_400 mute_text position-relative text-white">{{auth()->user()->email}} </span><br/>
                        @endif
                        @if(auth()->user()->phone)
                            <span  class="number_text font_14 f_w_400 mute_text text-white">{{auth()->user()->phone}}</span>
                        @endif

                    </div>
                    <a href="{{url('/profile')}}" class="amaz_primary_btn d-inline-flex align-items-center gap-2 ms-auto">{{__('common.edit')}}</a>
                </div>
            </div>
            <!-- @if (auth()->user()->role->type == 'customer' && isModuleActive('MultiVendor'))
                <a href="{{route('frontend.merchant-register-step-first')}}" target="_blank" class="amaz_primary_btn bg-gray d-block text-center mb-4">{{ __('common.convert_as_seller') }}</a>
            @endif -->

            @if(isModuleActive('Affiliate'))
                @if(empty(auth()->user()->affiliate_request) )
                  <a href="{{ route('affiliate.customerJoinAffiliate') }}"  class="amaz_primary_btn bg-gray d-block text-center mb-4">{{ __('common.join_affiliate') }}</a>
                @endif

                @if(auth()->user()->affiliate_request == 1)
                    @if(auth()->user()->accept_affiliate_request == 0)
                        <a href="javascript:void(0)" target="_blank" class="amaz_primary_btn bg-gray d-block text-center mb-4">{{ __('common.join_affiliate') }}</a>
                    @elseif(auth()->user()->accept_affiliate_request == 1)
                        <a href="{{ route('affiliate.my_affiliate.index') }}" target="_blank" class="amaz_primary_btn bg-gray d-block text-center mb-4">{{ __('common.affiliate_profile') }}</a>
                    @endif
                @endif
            @endif

            {{-- <div class="customer-blance d-flex align-items-end justify-content-between rounded-3 flex-wrap gap-2 border-1">
                <div  class="flex-grow-1">
                    <div class="w-100 text-center">
                        <span>{{__('common.total_balance')}}</span>
                        <p class="fw-bold" id="total_balance">{{ auth()->check()?single_price(auth()->user()->CustomerCurrentWalletAmounts):single_price(0.00) }}</p>
                    </div>
                </div>
                @if(url()->current() != url('/wallet/my-wallet-create'))
                <div class="flex-grow-1">
                    <a href="#" data-bs-toggle="modal" data-bs-target="#recharge_wallet"  class="amaz_primary_btn d-inline-flex align-items-center gap-1 ms-auto w-100 justify-content-center">
                        <svg  width="17.999" height="17.999" viewBox="0 0 17.999 17.999">
                        <path id="puls" d="M1.17853 10.1138L0.681641 10.1695L0.681642 10.1695L1.17853 10.1138ZM3.84368 12.7565L3.89657 12.2593L3.89655 12.2593L3.84368 12.7565ZM7.00001 12.958V13.458V12.958ZM10.1563 12.7565L10.1035 12.2593L10.1034 12.2593L10.1563 12.7565ZM12.8214 10.1138L12.3246 10.0581V10.0581L12.8214 10.1138ZM12.8214 3.80216L12.3246 3.85787L12.8214 3.80216ZM10.1563 1.15948L10.1034 1.65668L10.1563 1.15948ZM7.00001 0.958008V1.45801V0.958008ZM1.57657 4.91523C1.59713 4.63986 1.39056 4.39995 1.11518 4.37939C0.839808 4.35883 0.599905 4.5654 0.579346 4.84078L1.57657 4.91523ZM4.7887 0.564685C4.5136 0.588618 4.30998 0.831036 4.33392 1.10614C4.35785 1.38124 4.60027 1.58486 4.87537 1.56092L4.7887 0.564685ZM7.82099 5.57655C8.00598 5.78156 8.32215 5.79779 8.52716 5.61279C8.73217 5.42779 8.7484 5.11162 8.5634 4.90661L7.82099 5.57655ZM6.8905 4.76016L6.93303 5.25835H6.93303L6.8905 4.76016ZM5.88698 7.76266C5.68246 7.57712 5.36626 7.5925 5.18071 7.79702C4.99517 8.00154 5.01055 8.31775 5.21507 8.50329L5.88698 7.76266ZM7.489 3.79295C7.489 3.5168 7.26514 3.29295 6.989 3.29295C6.71286 3.29295 6.489 3.5168 6.489 3.79295H7.489ZM6.489 4.75421C6.489 5.03035 6.71286 5.25421 6.989 5.25421C7.26514 5.25421 7.489 5.03035 7.489 4.75421H6.489ZM7.489 8.59166C7.489 8.31552 7.26514 8.09166 6.989 8.09166C6.71286 8.09166 6.489 8.31552 6.489 8.59166H7.489ZM6.489 9.55294C6.489 9.82908 6.71286 10.0529 6.989 10.0529C7.26514 10.0529 7.489 9.82908 7.489 9.55294H6.489ZM4.55056 3.13756C4.8267 3.13756 5.05056 2.9137 5.05056 2.63756C5.05056 2.36142 4.8267 2.13756 4.55056 2.13756V3.13756ZM1.17071 2.13756C0.894569 2.13756 0.670711 2.36142 0.670711 2.63756C0.670711 2.9137 0.894569 3.13756 1.17071 3.13756L1.17071 2.13756ZM2.36063 4.31801C2.36063 4.59415 2.58449 4.81801 2.86063 4.81801C3.13678 4.81801 3.36063 4.59415 3.36063 4.31801L2.36063 4.31801ZM3.36063 0.958008C3.36063 0.681866 3.13678 0.458008 2.86063 0.458008C2.58449 0.458008 2.36063 0.681866 2.36063 0.958008L3.36063 0.958008ZM0.681642 10.1695C0.86286 11.7859 2.16718 13.0811 3.79082 13.2537L3.89655 12.2593C2.7377 12.1361 1.80418 11.2066 1.67542 10.0581L0.681642 10.1695ZM3.79079 13.2537C4.82256 13.3635 5.89802 13.458 7.00001 13.458V12.458C5.95211 12.458 4.91881 12.368 3.89657 12.2593L3.79079 13.2537ZM7.00001 13.458C8.10199 13.458 9.17744 13.3635 10.2092 13.2537L10.1034 12.2593C9.0812 12.368 8.0479 12.458 7.00001 12.458V13.458ZM10.2092 13.2537C11.8328 13.081 13.1371 11.7859 13.3183 10.1695L12.3246 10.0581C12.1958 11.2066 11.2623 12.1361 10.1035 12.2593L10.2092 13.2537ZM13.3183 10.1695C13.5606 8.00921 13.5606 5.90679 13.3183 3.74645L12.3246 3.85787C12.5585 5.94417 12.5585 7.97184 12.3246 10.0581L13.3183 10.1695ZM13.3183 3.74645C13.1371 2.13015 11.8328 0.834968 10.2092 0.662285L10.1034 1.65668C11.2623 1.77993 12.1958 2.70945 12.3246 3.85787L13.3183 3.74645ZM10.2092 0.662285C9.17743 0.552545 8.10198 0.458008 7.00001 0.458008V1.45801C8.04791 1.45801 9.08121 1.54795 10.1034 1.65668L10.2092 0.662285ZM0.579346 4.84078C0.446378 6.6218 0.480567 8.37594 0.681641 10.1695L1.67542 10.0581C1.48115 8.3253 1.4483 6.63339 1.57657 4.91523L0.579346 4.84078ZM7.00001 0.458008C6.24795 0.458008 5.50924 0.501999 4.7887 0.564685L4.87537 1.56092C5.57814 1.49978 6.28618 1.45801 7.00001 1.45801V0.458008ZM8.19219 5.24158C8.5634 4.90661 8.56325 4.90645 8.56311 4.90629C8.56306 4.90623 8.56291 4.90607 8.5628 4.90595C8.5626 4.90573 8.56238 4.90549 8.56216 4.90525C8.56172 4.90476 8.56124 4.90423 8.56073 4.90367C8.55971 4.90256 8.55855 4.9013 8.55727 4.89992C8.5547 4.89715 8.5516 4.89385 8.54799 4.89007C8.54076 4.8825 8.53142 4.87295 8.52002 4.86173C8.49726 4.83932 8.46607 4.81006 8.42678 4.77651C8.34858 4.70971 8.23613 4.62401 8.09211 4.54139C7.80394 4.37607 7.37724 4.21678 6.84796 4.26197L6.93303 5.25835C7.20898 5.23479 7.43313 5.31621 7.59449 5.40879C7.67525 5.45512 7.73724 5.50266 7.77729 5.53687C7.79713 5.55382 7.81102 5.56701 7.81855 5.57442C7.8223 5.57811 7.82442 5.58032 7.82485 5.58077C7.82507 5.581 7.82486 5.58078 7.82422 5.58009C7.8239 5.57975 7.82347 5.57928 7.82293 5.57869C7.82266 5.5784 7.82236 5.57807 7.82204 5.57772C7.82188 5.57754 7.82171 5.57735 7.82153 5.57716C7.82145 5.57706 7.82131 5.57691 7.82126 5.57686C7.82113 5.57671 7.82099 5.57655 8.19219 5.24158ZM6.84796 4.26197C5.83422 4.34853 5.19712 5.01356 5.19712 5.81388H6.19712C6.19712 5.62787 6.33631 5.3093 6.93303 5.25835L6.84796 4.26197ZM5.19712 5.81388C5.19712 6.06822 5.25311 6.30259 5.37601 6.50666C5.49839 6.70984 5.66727 6.85009 5.84144 6.94828C6.1635 7.12984 6.56883 7.19756 6.85403 7.25012C7.18953 7.31196 7.40679 7.35908 7.55041 7.44286C7.60944 7.47729 7.63724 7.50839 7.65332 7.5359C7.66924 7.56313 7.69219 7.61845 7.69219 7.73041H8.69219C8.69219 7.47363 8.63717 7.23744 8.51664 7.03125C8.39628 6.82533 8.22915 6.68108 8.05427 6.57907C7.73006 6.38995 7.32355 6.31982 7.03528 6.26669C6.69671 6.20429 6.47827 6.15933 6.33251 6.07716C6.27277 6.04348 6.24672 6.0141 6.23265 5.99073C6.2191 5.96824 6.19712 5.91912 6.19712 5.81388H5.19712ZM7.69219 7.73041C7.69219 7.73191 7.69235 7.74122 7.68114 7.76158C7.66893 7.78375 7.64376 7.81743 7.59659 7.85705C7.49994 7.93824 7.34079 8.01878 7.13459 8.06386C6.71909 8.15471 6.23502 8.07841 5.88698 7.76266L5.21507 8.50329C5.85311 9.08214 6.68963 9.18477 7.34819 9.04078C7.67902 8.96845 7.99527 8.82814 8.23977 8.62277C8.48291 8.41853 8.69219 8.11403 8.69219 7.73041H7.69219ZM6.489 3.79295V4.75421H7.489V3.79295H6.489ZM6.489 8.59166V9.55294H7.489V8.59166H6.489ZM4.55056 2.13756L1.17071 2.13756L1.17071 3.13756L4.55056 3.13756V2.13756ZM3.36063 4.31801L3.36063 0.958008L2.36063 0.958008L2.36063 4.31801L3.36063 4.31801Z" fill="currentColor"/></svg>
                        <span class="text-capitalize">{{__('wallet.recharge_wallet')}}</span>
                    </a>
                </div>
                @endif
            </div> --}}
            @if(auth()->user()->LastRehcarge)
                <div class="customer-transaction">
                    {{-- <div class="head d-flex align-items-center justify-content-between mb-3">
                        <p>{{__('wallet.last_transaction')}}</p>
                        <a href="{{route('my-wallet.index', 'customer')}}">{{__('common.view_all')}}</a>
                    </div> --}}
                    <div class="customer-transaction-card">
                        @foreach(auth()->user()->LastRehcarge as $lastrehcarge)
                        <div class="d-flex align-items-center justify-content-between list">
                            <p>{{dateConvert($lastrehcarge->created_at)}}</p>
                                @php
                                    switch ($lastrehcarge->type) {
                                        case 'Deposite':
                                        echo "<p class='text-sucess'>".__("wallet.deposite")."</p>";
                                        break;
                                        case 'Cart Payment':
                                        echo "<p class='text-danger'>".__("wallet.cart_payment")."</p>";
                                        break;
                                        case 'Refund Back':
                                        echo "<p class='text-sucess'>".__("wallet.refund_back")."</p>";
                                        break;
                                        case 'Refund':
                                        echo "<p class='text-sucess'>".__("wallet.refund")."</p>";
                                        break;
                                        case 'Referral':
                                        echo "<p class='text-sucess'>".__("common.referral")."</p>";
                                        break;
                                        case 'Withdraw':
                                        echo "<p class='text-danger'>".__("wallet.withdraw")."</p>";
                                        break;
                                        case 'point':
                                        echo "<p class='text-sucess'>".__("clubpoint.point")."</p>";
                                        break;
                                        default:
                                        echo "<p class='text-danger'>".$lastrehcarge->type."</p>";
                                        break;
                                    }
                                @endphp
                                @php
                                    switch ($lastrehcarge->type) {
                                        case 'Refund Back':
                                        echo " <p class='text-sucess'> + ".single_price($lastrehcarge->amount)."</p>";
                                        break;
                                        case 'Refund':
                                        echo " <p class='text-sucess'> + ".single_price($lastrehcarge->amount)."</p>";
                                        break;
                                        case 'point':
                                        echo " <p class='text-sucess'> + ".single_price($lastrehcarge->amount)."</p>";
                                        break;
                                        case 'Deposite':
                                        echo " <p class='text-sucess'> + ".single_price($lastrehcarge->amount)."</p>";
                                        break;
                                        case 'Referral':
                                        echo " <p class='text-sucess'> + ".single_price($lastrehcarge->amount)."</p>";
                                        break;
                                        default:
                                        echo " <p class='text-danger'> - ".single_price($lastrehcarge->amount)."</p>";
                                        break;
                                    }
                                @endphp
                        </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- dashboard_sidebar_menuList -->
    <div class="card border-0 rounded-3">
        <div class="dashboard_sidebar_menuList p-3 rounded-3">
            <ul>
                <li>
                    <a class="position-relative d-flex align-items-center " href="{{url('/profile/dashboard')}}">
                        <svg  width="18" height="18" viewBox="0 0 18 18">
                            <g id="element-equal" transform="translate(-1.25 -1.25)">
                                <path id="Path_4124" data-name="Path 4124" d="M18.627,9.622H15.245A2.193,2.193,0,0,1,12.75,7.127V3.745A2.193,2.193,0,0,1,15.245,1.25h3.382a2.193,2.193,0,0,1,2.495,2.495V7.127A2.2,2.2,0,0,1,18.627,9.622ZM15.245,2.506c-.988,0-1.239.251-1.239,1.239V7.127c0,.988.251,1.239,1.239,1.239h3.382c.988,0,1.239-.251,1.239-1.239V3.745c0-.988-.251-1.239-1.239-1.239Z" transform="translate(-1.872 0)" fill="#fd4949"/>
                                <path id="Path_4125" data-name="Path 4125" d="M7.127,9.622H3.745c-1.683,0-2.495-.745-2.495-2.286v-3.8C1.25,2,2.07,1.25,3.745,1.25H7.127C8.81,1.25,9.622,2,9.622,3.536V7.328C9.622,8.877,8.8,9.622,7.127,9.622ZM3.745,2.506c-1.122,0-1.239.318-1.239,1.03V7.328c0,.72.117,1.03,1.239,1.03H7.127c1.122,0,1.239-.318,1.239-1.03V3.536c0-.72-.117-1.03-1.239-1.03Z" transform="translate(0 0)" fill="#fd4949"/>
                                <path id="Path_4126" data-name="Path 4126" d="M7.127,21.122H3.745A2.193,2.193,0,0,1,1.25,18.627V15.245A2.193,2.193,0,0,1,3.745,12.75H7.127a2.193,2.193,0,0,1,2.495,2.495v3.382A2.2,2.2,0,0,1,7.127,21.122ZM3.745,14.006c-.988,0-1.239.251-1.239,1.239v3.382c0,.988.251,1.239,1.239,1.239H7.127c.988,0,1.239-.251,1.239-1.239V15.245c0-.988-.251-1.239-1.239-1.239Z" transform="translate(0 -1.872)" fill="#fd4949"/>
                                <path id="Path_4127" data-name="Path 4127" d="M19.9,16.006H14.878a.628.628,0,1,1,0-1.256H19.9a.628.628,0,1,1,0,1.256Z" transform="translate(-2.116 -2.197)" fill="#fd4949"/>
                                <path id="Path_4128" data-name="Path 4128" d="M19.9,20.006H14.878a.628.628,0,1,1,0-1.256H19.9a.628.628,0,1,1,0,1.256Z" transform="translate(-2.116 -2.848)" fill="#fd4949"/>
                            </g>
                        </svg>
                    {{__('common.dashboard')}}</a>
                </li>
              
                <li>
                    <a class="position-relative d-flex align-items-center" href="{{route('frontend.resell_product_list')}}">
                    <svg  width="16.326" height="18" viewBox="0 0 16.326 18">
                    <g  transform="translate(-2.25 -1.25)">
                        <path  data-name="Path 4129" d="M13.762,19.25h-6.7c-3.056,0-4.814-1.758-4.814-4.814V6.064c0-3.056,1.758-4.814,4.814-4.814h6.7c3.056,0,4.814,1.758,4.814,4.814v8.372C18.576,17.492,16.817,19.25,13.762,19.25ZM7.064,2.506c-2.394,0-3.558,1.164-3.558,3.558v8.372c0,2.394,1.164,3.558,3.558,3.558h6.7c2.394,0,3.558-1.164,3.558-3.558V6.064c0-2.394-1.164-3.558-3.558-3.558Z" transform="translate(0 0)" fill="#00124e"/>
                        <path  data-name="Path 4130" d="M8.8,9.515A1.037,1.037,0,0,1,7.75,8.476v-6.6a.633.633,0,0,1,.629-.629h5.868a.633.633,0,0,1,.629.629V8.467a1.025,1.025,0,0,1-.629.956,1.046,1.046,0,0,1-1.132-.193l-1.8-1.651-1.8,1.66A1.044,1.044,0,0,1,8.8,9.515Zm2.515-3.269a1.06,1.06,0,0,1,.713.277l1.593,1.467V2.507H9.007V7.989L10.6,6.523A1.06,1.06,0,0,1,11.313,6.246Z" transform="translate(-0.9)" fill="#00124e"/>
                        <path  data-name="Path 4131" d="M16.691,14.507H13.129a.629.629,0,0,1,0-1.257h3.563a.629.629,0,1,1,0,1.257Z" transform="translate(-1.673 -1.954)" fill="#00124e"/>
                        <path  data-name="Path 4132" d="M16,18.507H8.879a.629.629,0,0,1,0-1.257H16a.629.629,0,1,1,0,1.257Z" transform="translate(-0.983 -2.606)" fill="#00124e"/>
                    </g>
                    </svg>
                     {{__('amazy.Resell Product')}}</a>
                </li>
                <li>
                    <a class="position-relative d-flex align-items-center" href="{{route('frontend.my_purchase_histories')}}">
                    <svg  width="16.326" height="18" viewBox="0 0 16.326 18">
                    <g  transform="translate(-2.25 -1.25)">
                        <path  data-name="Path 4129" d="M13.762,19.25h-6.7c-3.056,0-4.814-1.758-4.814-4.814V6.064c0-3.056,1.758-4.814,4.814-4.814h6.7c3.056,0,4.814,1.758,4.814,4.814v8.372C18.576,17.492,16.817,19.25,13.762,19.25ZM7.064,2.506c-2.394,0-3.558,1.164-3.558,3.558v8.372c0,2.394,1.164,3.558,3.558,3.558h6.7c2.394,0,3.558-1.164,3.558-3.558V6.064c0-2.394-1.164-3.558-3.558-3.558Z" transform="translate(0 0)" fill="#00124e"/>
                        <path  data-name="Path 4130" d="M8.8,9.515A1.037,1.037,0,0,1,7.75,8.476v-6.6a.633.633,0,0,1,.629-.629h5.868a.633.633,0,0,1,.629.629V8.467a1.025,1.025,0,0,1-.629.956,1.046,1.046,0,0,1-1.132-.193l-1.8-1.651-1.8,1.66A1.044,1.044,0,0,1,8.8,9.515Zm2.515-3.269a1.06,1.06,0,0,1,.713.277l1.593,1.467V2.507H9.007V7.989L10.6,6.523A1.06,1.06,0,0,1,11.313,6.246Z" transform="translate(-0.9)" fill="#00124e"/>
                        <path  data-name="Path 4131" d="M16.691,14.507H13.129a.629.629,0,0,1,0-1.257h3.563a.629.629,0,1,1,0,1.257Z" transform="translate(-1.673 -1.954)" fill="#00124e"/>
                        <path  data-name="Path 4132" d="M16,18.507H8.879a.629.629,0,0,1,0-1.257H16a.629.629,0,1,1,0,1.257Z" transform="translate(-0.983 -2.606)" fill="#00124e"/>
                    </g>
                    </svg>
                     {{__('amazy.Purchase History')}}</a>
                </li>
                <li>
                    <a class="position-relative d-flex align-items-center" href="{{route('frontend.my-wishlist')}}">
                    <svg  width="16.326" height="18" viewBox="0 0 16.326 18">
                    <g  transform="translate(-2.25 -1.25)">
                        <path  data-name="Path 4129" d="M13.762,19.25h-6.7c-3.056,0-4.814-1.758-4.814-4.814V6.064c0-3.056,1.758-4.814,4.814-4.814h6.7c3.056,0,4.814,1.758,4.814,4.814v8.372C18.576,17.492,16.817,19.25,13.762,19.25ZM7.064,2.506c-2.394,0-3.558,1.164-3.558,3.558v8.372c0,2.394,1.164,3.558,3.558,3.558h6.7c2.394,0,3.558-1.164,3.558-3.558V6.064c0-2.394-1.164-3.558-3.558-3.558Z" transform="translate(0 0)" fill="#00124e"/>
                        <path  data-name="Path 4130" d="M8.8,9.515A1.037,1.037,0,0,1,7.75,8.476v-6.6a.633.633,0,0,1,.629-.629h5.868a.633.633,0,0,1,.629.629V8.467a1.025,1.025,0,0,1-.629.956,1.046,1.046,0,0,1-1.132-.193l-1.8-1.651-1.8,1.66A1.044,1.044,0,0,1,8.8,9.515Zm2.515-3.269a1.06,1.06,0,0,1,.713.277l1.593,1.467V2.507H9.007V7.989L10.6,6.523A1.06,1.06,0,0,1,11.313,6.246Z" transform="translate(-0.9)" fill="#00124e"/>
                        <path  data-name="Path 4131" d="M16.691,14.507H13.129a.629.629,0,0,1,0-1.257h3.563a.629.629,0,1,1,0,1.257Z" transform="translate(-1.673 -1.954)" fill="#00124e"/>
                        <path  data-name="Path 4132" d="M16,18.507H8.879a.629.629,0,0,1,0-1.257H16a.629.629,0,1,1,0,1.257Z" transform="translate(-0.983 -2.606)" fill="#00124e"/>
                    </g>
                    </svg>
                    {{__('customer_panel.my_wishlist')}}</a>
                </li>
                <li>
                    <a class="position-relative d-flex align-items-center" href="{{ route('frontend.my_purchase_order_list') }}">
                    <svg  width="18.121" height="18" viewBox="0 0 18.121 18">
                    <path  d="M15.085,19.32a3.477,3.477,0,0,1-1.737-.565l-2.521-1.492a1.2,1.2,0,0,0-1.02,0L7.278,18.755c-1.492.885-2.369.531-2.765.244s-.995-1.02-.6-2.706l.6-2.588a1.19,1.19,0,0,0-.27-.936L2.152,10.679A2.289,2.289,0,0,1,1.334,8.3,2.316,2.316,0,0,1,3.383,6.843L6.073,6.4a1.206,1.206,0,0,0,.725-.54L8.29,2.88c.674-1.357,1.56-1.56,2.023-1.56s1.349.2,2.023,1.56l1.484,2.968a1.248,1.248,0,0,0,.733.54l2.689.447a2.291,2.291,0,0,1,2.049,1.459,2.324,2.324,0,0,1-.818,2.378l-2.091,2.1a1.213,1.213,0,0,0-.27.936l.6,2.588c.388,1.686-.211,2.42-.6,2.706A1.725,1.725,0,0,1,15.085,19.32Zm-4.772-3.44a2.308,2.308,0,0,1,1.155.3l2.521,1.492c.733.438,1.2.438,1.374.312s.3-.573.118-1.4l-.6-2.588a2.466,2.466,0,0,1,.607-2.116l2.091-2.091c.413-.413.6-.818.514-1.1s-.481-.506-1.054-.6l-2.689-.447A2.471,2.471,0,0,1,12.7,6.421L11.215,3.453c-.27-.54-.607-.86-.9-.86s-.632.32-.894.86L7.927,6.421A2.471,2.471,0,0,1,6.275,7.643L3.594,8.09c-.573.093-.961.32-1.054.6s.1.691.514,1.1l2.091,2.091a2.459,2.459,0,0,1,.607,2.116l-.6,2.588c-.194.835-.059,1.273.118,1.4s.632.118,1.374-.312l2.521-1.492A2.254,2.254,0,0,1,10.313,15.88Z" transform="translate(-1.25 -1.32)" fill="#00124e"/>
                    </svg>
                    {{__('order.my_order')}}</a>
                </li>
                <li>
                    <a class="position-relative d-flex align-items-center" href="{{ route('frontend.purchased-gift-card') }}">
                    <svg  width="18" height="17.998" viewBox="0 0 18 17.998">
                    <g id="bag" transform="translate(-1.25 -1.254)">
                        <path id="Path_4133" data-name="Path 4133" d="M5.069,5.547a.642.642,0,0,1-.444-.184.632.632,0,0,1,0-.887L7.665,1.436a.628.628,0,0,1,.887.887L5.513,5.363A.658.658,0,0,1,5.069,5.547Z" transform="translate(-0.52)" fill="#00124e"/>
                        <path id="Path_4134" data-name="Path 4134" d="M18.1,5.547a.621.621,0,0,1-.444-.184L14.614,2.323a.628.628,0,0,1,.887-.887L18.54,4.475a.632.632,0,0,1,0,.887A.642.642,0,0,1,18.1,5.547Z" transform="translate(-2.146)" fill="#00124e"/>
                        <path id="Path_4135" data-name="Path 4135" d="M17.124,9.706H3.552a2.255,2.255,0,0,1-1.741-.477A2.433,2.433,0,0,1,1.25,7.4c0-2.3,1.683-2.3,2.487-2.3H16.764c.8,0,2.487,0,2.487,2.3a2.421,2.421,0,0,1-.561,1.825A2.076,2.076,0,0,1,17.124,9.706ZM3.737,8.45h13.22c.377.008.728.008.846-.109.059-.059.184-.26.184-.938,0-.946-.234-1.047-1.231-1.047H3.737c-1,0-1.231.1-1.231,1.047,0,.678.134.879.184.938a1.685,1.685,0,0,0,.846.109Z" transform="translate(0 -0.626)" fill="#00124e"/>
                        <path id="Path_4136" data-name="Path 4136" d="M9.638,17.478a.632.632,0,0,1-.628-.628V13.878a.628.628,0,0,1,1.256,0V16.85A.627.627,0,0,1,9.638,17.478Z" transform="translate(-1.263 -1.953)" fill="#00124e"/>
                        <path id="Path_4137" data-name="Path 4137" d="M14.237,17.478a.632.632,0,0,1-.628-.628V13.878a.628.628,0,0,1,1.256,0V16.85A.627.627,0,0,1,14.237,17.478Z" transform="translate(-2.012 -1.953)" fill="#00124e"/>
                        <path id="Path_4138" data-name="Path 4138" d="M12.915,20.553H7.866c-3,0-3.667-1.783-3.927-3.332L2.759,9.979A.628.628,0,0,1,4,9.778l1.18,7.234c.243,1.482.745,2.286,2.688,2.286h5.048c2.152,0,2.394-.754,2.671-2.21l1.407-7.326A.627.627,0,0,1,18.223,10L16.816,17.33C16.49,19.029,15.945,20.553,12.915,20.553Z" transform="translate(-0.244 -1.302)" fill="#00124e"/>
                    </g>
                    </svg>
                    {{__('marketing.giftcard')}}</a>
                </li>
                <li>
                    <a class="position-relative d-flex align-items-center {{ request()->is('wallet/*') ?'active' : '' }}" href="{{route('my-wallet.index', 'customer')}}">
                    <svg  width="18" height="18.002" viewBox="0 0 18 18.002">
                    <g  transform="translate(-1.25 -1.254)">
                        <path  data-name="Path 4166" d="M14.436,20.068H6.064A4.558,4.558,0,0,1,1.25,15.253V11.065A4.518,4.518,0,0,1,5.31,6.309a5.391,5.391,0,0,1,.753-.059h8.372a4.764,4.764,0,0,1,.728.05,4.514,4.514,0,0,1,4.086,4.765v4.187A4.558,4.558,0,0,1,14.436,20.068ZM6.064,7.506a4.489,4.489,0,0,0-.586.042,3.286,3.286,0,0,0-2.972,3.517v4.187a3.336,3.336,0,0,0,3.558,3.559h8.372a3.336,3.336,0,0,0,3.558-3.559V11.065A3.288,3.288,0,0,0,14.989,7.54a3.2,3.2,0,0,0-.553-.034Z" transform="translate(0 -0.812)" fill="#00124e"/>
                        <path  data-name="Path 4167" d="M6.069,6.746a.631.631,0,0,1-.511-.26.616.616,0,0,1-.05-.653,3.008,3.008,0,0,1,.6-.812l2.72-2.73a3.581,3.581,0,0,1,5.039,0l1.465,1.482a3.478,3.478,0,0,1,1.038,2.3.63.63,0,0,1-.209.5.623.623,0,0,1-.519.151,3.564,3.564,0,0,0-.527-.033H6.747a4.488,4.488,0,0,0-.586.042A.314.314,0,0,1,6.069,6.746Zm1.4-1.306h7.5a2.23,2.23,0,0,0-.519-.779L12.975,3.17a2.334,2.334,0,0,0-3.264,0Z" transform="translate(-0.681)" fill="#00124e"/>
                        <path  data-name="Path 4168" d="M21.065,16.356H18.553a2.3,2.3,0,0,1,0-4.606h2.512a.628.628,0,1,1,0,1.256H18.553a1.047,1.047,0,0,0,0,2.094h2.512a.628.628,0,1,1,0,1.256Z" transform="translate(-2.443 -1.706)" fill="#00124e"/>
                    </g>
                    </svg>
                    {{__('wallet.my_wallet')}}</a>
                </li>
                @if(isModuleActive('ClubPoint'))
                <li>
                    <a class="position-relative d-flex align-items-center {{ request()->is('support-ticket/*') ?'active' : '' }}" href="{{route('clubpoint.frontend.earning-points')}}">
                        <svg width="18" height="20" viewBox="0 0 18 20" fill="none" >
                            <path d="M4.70495 18.8625L4.76695 18.0076L4.76694 18.0076L4.70495 18.8625ZM13.3792 18.8625L13.3172 18.0076L13.3172 18.0076L13.3792 18.8625ZM16.9653 15.2749L17.8196 15.3444L17.8196 15.3442L16.9653 15.2749ZM16.9653 9.56532L16.1109 9.63471V9.63471L16.9653 9.56532ZM13.3792 5.97769L13.3172 6.83258L13.3172 6.83258L13.3792 5.97769ZM4.70495 5.97769L4.76695 6.83258L4.76696 6.83258L4.70495 5.97769ZM1.11898 9.56532L1.97331 9.63471L1.97331 9.63471L1.11898 9.56532ZM1.11898 15.2749L0.264652 15.3443L0.264652 15.3443L1.11898 15.2749ZM9.04202 5.81295L8.31398 6.26534C8.47038 6.51703 8.74569 6.67009 9.04202 6.67009C9.33835 6.67009 9.61366 6.51703 9.77006 6.26534L9.04202 5.81295ZM11.3245 2.13965L12.0526 2.59204L12.0526 2.59204L11.3245 2.13965ZM15.5194 2.27772L14.7629 2.68065L14.7629 2.68067L15.5194 2.27772ZM14.4419 5.55281L14.0686 4.78124L14.0685 4.78125L14.4419 5.55281ZM13.1704 5.21584C12.7443 5.42203 12.566 5.93463 12.7722 6.36075C12.9784 6.78687 13.491 6.96515 13.9171 6.75895L13.1704 5.21584ZM6.75952 2.13965L6.03148 2.59204L6.03148 2.59204L6.75952 2.13965ZM2.56453 2.27772L3.32106 2.68066L3.32106 2.68065L2.56453 2.27772ZM3.64208 5.55281L3.26875 6.32438L3.26876 6.32438L3.64208 5.55281ZM4.16693 6.75897C4.59306 6.96515 5.10565 6.78685 5.31183 6.36072C5.51801 5.9346 5.33971 5.42201 4.91358 5.21582L4.16693 6.75897ZM4.64294 19.7174C5.95759 19.8128 7.40943 19.8571 9.04208 19.8571V18.1429C7.43922 18.1429 6.03019 18.0993 4.76695 18.0076L4.64294 19.7174ZM9.04208 19.8571C10.6747 19.8571 12.1266 19.8128 13.4412 19.7174L13.3172 18.0076C12.054 18.0993 10.645 18.1429 9.04208 18.1429V19.8571ZM13.4412 19.7174C15.7971 19.5466 17.6298 17.6794 17.8196 15.3444L16.1109 15.2055C15.988 16.7171 14.8105 17.8994 13.3172 18.0076L13.4412 19.7174ZM17.8196 15.3442C17.8969 14.3906 17.9412 13.4168 17.9412 12.42H16.227C16.227 13.3658 16.1849 14.2932 16.1109 15.2057L17.8196 15.3442ZM17.9412 12.42C17.9412 11.4234 17.8971 10.4496 17.8196 9.49592L16.1109 9.63471C16.185 10.5469 16.227 11.4742 16.227 12.42H17.9412ZM17.8196 9.49593C17.6299 7.1608 15.7971 5.29365 13.4412 5.12279L13.3172 6.83258C14.8106 6.94089 15.9882 8.12318 16.1109 9.63471L17.8196 9.49593ZM13.4412 5.12279C12.1266 5.02742 10.6747 4.98312 9.04208 4.98312V6.69741C10.645 6.69741 12.054 6.74095 13.3172 6.83258L13.4412 5.12279ZM9.04208 4.98312C7.40945 4.98312 5.9576 5.02742 4.64293 5.12279L4.76696 6.83258C6.03018 6.74095 7.4392 6.69741 9.04208 6.69741V4.98312ZM4.64295 5.12279C2.28705 5.29365 0.454306 7.16083 0.264652 9.49593L1.97331 9.63471C2.09608 8.12315 3.2736 6.94089 4.76695 6.83258L4.64295 5.12279ZM0.264652 9.49592C0.187194 10.4495 0.142857 11.4234 0.142857 12.42H1.85714C1.85714 11.4743 1.89921 10.547 1.97331 9.63471L0.264652 9.49592ZM0.142857 12.42C0.142857 13.4168 0.187194 14.3906 0.264652 15.3443L1.97331 15.2056C1.89921 14.2932 1.85714 13.3658 1.85714 12.42H0.142857ZM0.264652 15.3443C0.454306 17.6794 2.28704 19.5466 4.64296 19.7174L4.76694 18.0076C3.27361 17.8994 2.09608 16.7171 1.97331 15.2056L0.264652 15.3443ZM1 11.815H17.0841V10.1007H1V11.815ZM9.89922 19V5.84026H8.18493V19H9.89922ZM9.77006 6.26534L12.0526 2.59204L10.5965 1.68727L8.31398 5.36057L9.77006 6.26534ZM12.0526 2.59204C12.6857 1.57304 14.2002 1.62413 14.7629 2.68065L16.276 1.87479C15.0914 -0.349321 11.9259 -0.45225 10.5965 1.68727L12.0526 2.59204ZM14.7629 2.68067C15.1702 3.44537 14.8617 4.39747 14.0686 4.78124L14.8152 6.32439C16.4767 5.52049 17.1445 3.50534 16.276 1.87477L14.7629 2.68067ZM14.0685 4.78125L13.1704 5.21584L13.9171 6.75895L14.8152 6.32437L14.0685 4.78125ZM9.77006 5.36057L7.48756 1.68727L6.03148 2.59204L8.31398 6.26534L9.77006 5.36057ZM7.48756 1.68727C6.15812 -0.452245 2.99255 -0.349329 1.80799 1.87479L3.32106 2.68065C3.88375 1.62414 5.3983 1.57303 6.03148 2.59204L7.48756 1.68727ZM1.808 1.87479C0.93955 3.50534 1.60731 5.52048 3.26875 6.32438L4.01541 4.78124C3.22229 4.39748 2.91376 3.44537 3.32106 2.68066L1.808 1.87479ZM3.26876 6.32438L4.16693 6.75897L4.91358 5.21582L4.01541 4.78124L3.26876 6.32438Z" fill="#4277FF"/>
                        </svg>

                    {{__('common.earning_points')}}</a>
                </li>
                @endif
                <li>
                    <a class="position-relative d-flex align-items-center" href="{{url('/profile/coupons')}}">
                    <svg  width="17.167" height="18.001" viewBox="0 0 17.167 18.001">
                    <g id="gift" transform="translate(-1.75 -1.254)">
                        <path id="Path_4154" data-name="Path 4154" d="M13.9,20.555H7.2c-2.864,0-3.978-1.114-3.978-3.978v-6.7a.633.633,0,0,1,.628-.628h13.4a.633.633,0,0,1,.628.628v6.7C17.873,19.441,16.759,20.555,13.9,20.555ZM4.475,10.506v6.071c0,2.16.561,2.722,2.722,2.722h6.7c2.16,0,2.721-.561,2.721-2.722V10.506Z" transform="translate(-0.239 -1.3)" fill="#00124e"/>
                        <path id="Path_4155" data-name="Path 4155" d="M16.614,9.693H4.053a2.094,2.094,0,0,1-2.3-2.3V6.553a2.094,2.094,0,0,1,2.3-2.3H16.614a2.134,2.134,0,0,1,2.3,2.3V7.39A2.134,2.134,0,0,1,16.614,9.693ZM4.053,5.506c-.762,0-1.047.285-1.047,1.047V7.39c0,.762.285,1.047,1.047,1.047H16.614c.737,0,1.047-.31,1.047-1.047V6.553c0-.737-.31-1.047-1.047-1.047Z" transform="translate(0 -0.487)" fill="#00124e"/>
                        <path id="Path_4156" data-name="Path 4156" d="M10.58,5.018H5.957a.633.633,0,0,1-.461-.2,1.419,1.419,0,0,1,.042-1.959L6.728,1.668a1.435,1.435,0,0,1,2.018,0l2.278,2.278a.631.631,0,0,1,.134.687A.612.612,0,0,1,10.58,5.018ZM6.418,3.762H9.073L7.858,2.556a.172.172,0,0,0-.243,0L6.426,3.745C6.426,3.754,6.418,3.754,6.418,3.762Z" transform="translate(-0.549)" fill="#00124e"/>
                        <path id="Path_4157" data-name="Path 4157" d="M16.851,5.018H12.228a.62.62,0,0,1-.578-.385.634.634,0,0,1,.134-.687l2.278-2.278a1.435,1.435,0,0,1,2.018,0l1.189,1.189a1.411,1.411,0,0,1,.042,1.959A.633.633,0,0,1,16.851,5.018Zm-3.1-1.256h2.655l-.017-.017L15.2,2.556a.172.172,0,0,0-.243,0Z" transform="translate(-1.602)" fill="#00124e"/>
                        <path id="Path_4158" data-name="Path 4158" d="M9.657,15.656a1.467,1.467,0,0,1-1.465-1.465V9.878a.633.633,0,0,1,.628-.628h5.058a.633.633,0,0,1,.628.628v4.3a1.462,1.462,0,0,1-2.278,1.214l-.745-.5a.2.2,0,0,0-.234,0l-.787.519A1.424,1.424,0,0,1,9.657,15.656Zm-.209-5.15v3.676a.211.211,0,0,0,.327.176l.787-.519a1.455,1.455,0,0,1,1.616,0l.745.5a.211.211,0,0,0,.327-.176V10.5h-3.8Z" transform="translate(-1.047 -1.3)" fill="#00124e"/>
                    </g>
                    </svg>
                    {{__('customer_panel.my_coupons')}}</a>
                </li>
                <li>
                    <a class="position-relative d-flex align-items-center" href="{{route('refund.frontend.index')}}">
                        <svg  width="18" height="18.002" viewBox="0 0 18 18.002">
                        <g  transform="translate(-1.25 -1.254)">
                            <path  data-name="Path 4166" d="M14.436,20.068H6.064A4.558,4.558,0,0,1,1.25,15.253V11.065A4.518,4.518,0,0,1,5.31,6.309a5.391,5.391,0,0,1,.753-.059h8.372a4.764,4.764,0,0,1,.728.05,4.514,4.514,0,0,1,4.086,4.765v4.187A4.558,4.558,0,0,1,14.436,20.068ZM6.064,7.506a4.489,4.489,0,0,0-.586.042,3.286,3.286,0,0,0-2.972,3.517v4.187a3.336,3.336,0,0,0,3.558,3.559h8.372a3.336,3.336,0,0,0,3.558-3.559V11.065A3.288,3.288,0,0,0,14.989,7.54a3.2,3.2,0,0,0-.553-.034Z" transform="translate(0 -0.812)" fill="#00124e"/>
                            <path  data-name="Path 4167" d="M6.069,6.746a.631.631,0,0,1-.511-.26.616.616,0,0,1-.05-.653,3.008,3.008,0,0,1,.6-.812l2.72-2.73a3.581,3.581,0,0,1,5.039,0l1.465,1.482a3.478,3.478,0,0,1,1.038,2.3.63.63,0,0,1-.209.5.623.623,0,0,1-.519.151,3.564,3.564,0,0,0-.527-.033H6.747a4.488,4.488,0,0,0-.586.042A.314.314,0,0,1,6.069,6.746Zm1.4-1.306h7.5a2.23,2.23,0,0,0-.519-.779L12.975,3.17a2.334,2.334,0,0,0-3.264,0Z" transform="translate(-0.681)" fill="#00124e"/>
                            <path  data-name="Path 4168" d="M21.065,16.356H18.553a2.3,2.3,0,0,1,0-4.606h2.512a.628.628,0,1,1,0,1.256H18.553a1.047,1.047,0,0,0,0,2.094h2.512a.628.628,0,1,1,0,1.256Z" transform="translate(-2.443 -1.706)" fill="#00124e"/>
                        </g>
                        </svg>
                        {{__('customer_panel.refund_dispute')}}</a>
                </li>
                <li>
                    <a class="position-relative d-flex align-items-center" href="{{url('/profile')}}">
                    <svg  width="18.005" height="18" viewBox="0 0 18.005 18">
                    <g id="profile-circle" transform="translate(-1.25 -1.25)">
                        <path id="Path_4163" data-name="Path 4163" d="M11.447,12.222h-.142a3.341,3.341,0,1,1,.167,0Zm-.1-5.494a2.12,2.12,0,0,0-.084,4.237.7.7,0,0,1,.193,0,2.12,2.12,0,0,0-.109-4.237Z" transform="translate(-1.094 -0.689)" fill="#00124e"/>
                        <path id="Path_4164" data-name="Path 4164" d="M10.783,21.441a8.969,8.969,0,0,1-6.071-2.362.632.632,0,0,1-.2-.528,3.692,3.692,0,0,1,1.759-2.613,8.813,8.813,0,0,1,9.028,0,3.708,3.708,0,0,1,1.759,2.613.6.6,0,0,1-.2.528A8.969,8.969,0,0,1,10.783,21.441ZM5.825,18.384a7.726,7.726,0,0,0,9.915,0,2.722,2.722,0,0,0-1.147-1.407,7.578,7.578,0,0,0-7.629,0A2.7,2.7,0,0,0,5.825,18.384Z" transform="translate(-0.53 -2.191)" fill="#00124e"/>
                        <path id="Path_4165" data-name="Path 4165" d="M10.252,19.25a9,9,0,1,1,9-9A9.009,9.009,0,0,1,10.252,19.25Zm0-16.744A7.744,7.744,0,1,0,18,10.25,7.754,7.754,0,0,0,10.252,2.506Z" fill="#00124e"/>
                    </g>
                    </svg>
                    {{__('customer_panel.my_account')}}</a>
                </li>
                <li>
                    <a class="position-relative d-flex align-items-center" href="{{ route('frontend.digital_product') }}">
                    <svg  width="17.607" height="17.999" viewBox="0 0 17.607 17.999">
                    <g id="wallet-minus" transform="translate(-1.488 -1.25)">
                        <path id="Path_4142" data-name="Path 4142" d="M13.564,16.506H9.378a.628.628,0,0,1,0-1.256h4.186a.628.628,0,0,1,0,1.256Z" transform="translate(-1.183 -2.28)" fill="#00124e"/>
                        <path id="Path_4143" data-name="Path 4143" d="M5.08,8.96a.658.658,0,0,1-.444-.184A.63.63,0,0,1,4.5,8.089L6.059,4.373a1.806,1.806,0,0,1,.075-.167C7.374,1.35,8.956.656,11.752,1.727a.616.616,0,0,1,.352.343.632.632,0,0,1,0,.494L9.651,8.257a.635.635,0,0,1-.578.377H6.687a3.457,3.457,0,0,0-1.365.276A.6.6,0,0,1,5.08,8.96ZM9.609,2.506c-1.038,0-1.674.678-2.336,2.218-.008.025-.025.05-.033.075l-1.1,2.6c.184-.017.36-.025.544-.025H8.654L10.672,2.69A3.506,3.506,0,0,0,9.609,2.506Z" transform="translate(-0.483)" fill="#00124e"/>
                        <path id="Path_4144" data-name="Path 4144" d="M16.811,8.886a.7.7,0,0,1-.184-.025,3.553,3.553,0,0,0-1-.142H9.846a.641.641,0,0,1-.527-.285.65.65,0,0,1-.05-.594L11.7,2.205a.688.688,0,0,1,.8-.4c.1.033.193.075.293.117l1.976.829A6.059,6.059,0,0,1,17.2,4.332a2.674,2.674,0,0,1,.26.368,2.241,2.241,0,0,1,.234.494,1.625,1.625,0,0,1,.092.285A4.651,4.651,0,0,1,17.4,8.5.64.64,0,0,1,16.811,8.886ZM10.8,7.463h4.83a4.91,4.91,0,0,1,.787.067,2.982,2.982,0,0,0,.151-1.741c-.017-.075-.033-.109-.042-.142A1.493,1.493,0,0,0,16.4,5.37a1.77,1.77,0,0,0-.167-.243A4.9,4.9,0,0,0,14.292,3.9L12.634,3.21Z" transform="translate(-1.259 -0.086)" fill="#00124e"/>
                        <path id="Path_4145" data-name="Path 4145" d="M13.555,20.441H7.025a5.752,5.752,0,0,1-.67-.042,4.724,4.724,0,0,1-4.814-4.847,4.868,4.868,0,0,1-.042-.636V13.284A4.7,4.7,0,0,1,6.213,8.57h8.171a4.56,4.56,0,0,1,1.365.2A4.751,4.751,0,0,1,19.1,13.284v1.632c0,.184-.008.36-.017.527C18.9,18.717,16.987,20.441,13.555,20.441ZM6.2,9.826a3.445,3.445,0,0,0-3.457,3.457v1.632c0,.176.017.352.033.519.159,2.386,1.331,3.558,3.684,3.717a4.54,4.54,0,0,0,.553.042h6.53c2.763,0,4.119-1.214,4.253-3.809.008-.151.017-.3.017-.469V13.284a3.5,3.5,0,0,0-2.453-3.315,3.552,3.552,0,0,0-1-.142Z" transform="translate(-0.002 -1.192)" fill="#00124e"/>
                        <path id="Path_4146" data-name="Path 4146" d="M2.116,13.3a.632.632,0,0,1-.628-.628V10.216A5.517,5.517,0,0,1,5.934,4.8a.652.652,0,0,1,.611.218.626.626,0,0,1,.092.636l-1.465,3.5a.67.67,0,0,1-.326.335,3.468,3.468,0,0,0-2.093,3.181A.64.64,0,0,1,2.116,13.3ZM4.929,6.491A4.274,4.274,0,0,0,2.836,9.379,4.57,4.57,0,0,1,4.117,8.45Z" transform="translate(0 -0.576)" fill="#00124e"/>
                        <path id="Path_4147" data-name="Path 4147" d="M21.075,13.386a.632.632,0,0,1-.628-.628,3.5,3.5,0,0,0-2.453-3.315.622.622,0,0,1-.4-.829,3.545,3.545,0,0,0,.335-2.244c-.017-.075-.033-.109-.042-.142a.629.629,0,0,1,.142-.712.622.622,0,0,1,.72-.1A5.5,5.5,0,0,1,21.7,10.3v2.453A.632.632,0,0,1,21.075,13.386Zm-2.1-4.906a4.591,4.591,0,0,1,1.39,1,4.2,4.2,0,0,0-1.13-2.152A6.213,6.213,0,0,1,18.974,8.48Z" transform="translate(-2.615 -0.667)" fill="#00124e"/>
                    </g>
                    </svg>
                    {{__('customer_panel.digital_products')}}</a>
                </li>
                <li>
                    <a class="position-relative d-flex align-items-center" href="{{url('/profile/referral')}}">
                    <svg  width="17.431" height="18" viewBox="0 0 17.431 18">
                    <g id="profile-2user" transform="translate(-1.602 -1.25)">
                        <path id="Path_4169" data-name="Path 4169" d="M8.33,10.014H8.262a.459.459,0,0,0-.152,0,4.348,4.348,0,1,1,.245,0Zm-.135-7.5a3.115,3.115,0,0,0-.118,6.228,1.407,1.407,0,0,1,.27,0,3.116,3.116,0,0,0-.152-6.228Z" transform="translate(-0.342)" fill="#00124e"/>
                        <path id="Path_4170" data-name="Path 4170" d="M16.389,10.433a.234.234,0,0,1-.076-.008.669.669,0,0,1-.735-.558.616.616,0,0,1,.524-.7c.1-.008.211-.008.3-.008a2.326,2.326,0,0,0-.127-4.648.626.626,0,0,1-.634-.625.638.638,0,0,1,.634-.634,3.593,3.593,0,0,1,.135,7.183Z" transform="translate(-2.164 -0.31)" fill="#00124e"/>
                        <path id="Path_4171" data-name="Path 4171" d="M8,20.984a8.348,8.348,0,0,1-4.58-1.268,3.626,3.626,0,0,1-1.817-3A3.664,3.664,0,0,1,3.419,13.7a8.948,8.948,0,0,1,9.161,0,3.646,3.646,0,0,1,1.817,3,3.664,3.664,0,0,1-1.817,3.017A8.368,8.368,0,0,1,8,20.984ZM4.12,14.764a2.43,2.43,0,0,0-1.251,1.961A2.425,2.425,0,0,0,4.12,18.668a7.637,7.637,0,0,0,7.758,0,2.43,2.43,0,0,0,1.251-1.961,2.425,2.425,0,0,0-1.251-1.944A7.677,7.677,0,0,0,4.12,14.764Z" transform="translate(0 -1.733)" fill="#00124e"/>
                        <path id="Path_4172" data-name="Path 4172" d="M18.223,19.587a.624.624,0,0,1-.617-.507.642.642,0,0,1,.49-.752,3.423,3.423,0,0,0,1.4-.617,1.508,1.508,0,0,0,.008-2.594,3.538,3.538,0,0,0-1.386-.617.637.637,0,1,1,.279-1.242,4.716,4.716,0,0,1,1.876.845,2.906,2.906,0,0,1,1.234,2.307,2.943,2.943,0,0,1-1.242,2.316,4.577,4.577,0,0,1-1.91.845A.4.4,0,0,1,18.223,19.587Z" transform="translate(-2.477 -1.858)" fill="#00124e"/>
                    </g>
                    </svg>
                        {{__('common.referral')}}</a>
                </li>
                <li>
                    <a class="position-relative d-flex align-items-center {{ request()->is('support-ticket/*') ?'active' : '' }}" href="{{url('/support-ticket')}}">
                        <svg width="21px" height="21px" viewBox="0 0 21 21" >
                            <g fill="none" fill-rule="evenodd" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"><circle cx="10.5" cy="10.5" r="8"/><circle cx="10.5" cy="10.5" r="4"/><path d="m13.5 7.5 2.5-2.5"/>
                                <path d="m13.5 13.5 2.5 2.5"/><path d="m7.5 13.5-2.5 2.5"/>
                                <path d="m7.5 7.5-2.5-2.5"/>
                            </g>
                        </svg>
                    {{__('ticket.support_ticket')}}</a>
                </li>
                @if(isModuleActive('MultiVendor'))
                    <li>
                        <a class="position-relative d-flex align-items-center" href="{{route('frontend.profile.follow-customer')}}">
                            <svg width="24px" height="24px" viewBox="0 0 24 24" >
                                <path d="M19.2928932,19 L17.1464466,16.8535534 C16.9511845,16.6582912 16.9511845,16.3417088 17.1464466,16.1464466 C17.3417088,15.9511845 17.6582912,15.9511845 17.8535534,16.1464466 L20.8535534,19.1464466 C21.0488155,19.3417088 21.0488155,19.6582912 20.8535534,19.8535534 L17.8535534,22.8535534 C17.6582912,23.0488155 17.3417088,23.0488155 17.1464466,22.8535534 C16.9511845,22.6582912 16.9511845,22.3417088 17.1464466,22.1464466 L19.2928932,20 L14.5,20 C14.2238576,20 14,19.7761424 14,19.5 C14,19.2238576 14.2238576,19 14.5,19 L19.2928932,19 L19.2928932,19 Z M14.0425135,13.5651442 C13.4188979,13.8445863 12.7275984,14 12,14 C11.2738711,14 10.5838946,13.8452135 9.96126583,13.5668358 L5.87929558,15.4222768 C5.34380416,15.665682 5,16.1996113 5,16.7878265 L5,17.5 C5,18.3284271 5.67157288,19 6.5,19 L11.5,19 C11.7761424,19 12,19.2238576 12,19.5 C12,19.7761424 11.7761424,20 11.5,20 L6.5,20 C5.11928813,20 4,18.8807119 4,17.5 L4,16.7878265 C4,15.8074678 4.57300693,14.9175857 5.46549264,14.5119103 L8.92215823,12.9406987 C7.75209123,12.0255364 7,10.6005984 7,9 C7,6.23857625 9.23857625,4 12,4 C14.7614237,4 17,6.23857625 17,9 C17,10.5929224 16.2551051,12.0118652 15.0946468,12.927497 L17.6966094,14.0402775 C17.9505071,14.1488619 18.0683068,14.4427117 17.9597225,14.6966094 C17.8511381,14.9505071 17.5572883,15.0683068 17.3033906,14.9597225 L14.0425135,13.5651442 L14.0425135,13.5651442 Z M12,13 C14.209139,13 16,11.209139 16,9 C16,6.790861 14.209139,5 12,5 C9.790861,5 8,6.790861 8,9 C8,11.209139 9.790861,13 12,13 Z"/>
                              </svg>
                        {{__('common.follow')}}</a>
                    </li>
                @endif
                <li>
                    <span class="amazy_bb2 d-block"></span>
                </li>
                <li>
                    <a class="position-relative d-flex align-items-center" href="{{route('frontend.notifications')}}">
                    <svg  width="14.593" height="18" viewBox="0 0 14.593 18">
                    <g id="notification" transform="translate(-3.227 -1.18)">
                        <path id="Path_4148" data-name="Path 4148" d="M10.545,17.452a18.038,18.038,0,0,1-5.717-.924,2.315,2.315,0,0,1-1.465-1.373,2.248,2.248,0,0,1,.225-1.981l.957-1.59a3.369,3.369,0,0,0,.383-1.39V7.789a5.618,5.618,0,0,1,11.235,0v2.405a3.478,3.478,0,0,0,.383,1.4l.949,1.581a2.3,2.3,0,0,1,.183,1.981,2.264,2.264,0,0,1-1.423,1.373A17.949,17.949,0,0,1,10.545,17.452Zm0-14.031A4.374,4.374,0,0,0,6.175,7.789v2.405a4.694,4.694,0,0,1-.558,2.031l-.957,1.59a1.034,1.034,0,0,0-.125.907,1.053,1.053,0,0,0,.691.616,16.644,16.644,0,0,0,10.644,0,1.016,1.016,0,0,0,.641-.624,1.037,1.037,0,0,0-.083-.9l-.957-1.59a4.677,4.677,0,0,1-.558-2.039v-2.4A4.369,4.369,0,0,0,10.545,3.42Z" transform="translate(0 -0.166)" fill="#00124e"/>
                        <path id="Path_4149" data-name="Path 4149" d="M13.133,3.477a.634.634,0,0,1-.175-.025,5.865,5.865,0,0,0-.7-.15,4.866,4.866,0,0,0-2.031.15.62.62,0,0,1-.757-.824,2.279,2.279,0,0,1,4.244,0,.635.635,0,0,1-.117.649A.643.643,0,0,1,13.133,3.477Z" transform="translate(-1.04)" fill="#00124e"/>
                        <path id="Path_4150" data-name="Path 4150" d="M11.39,22.179A3.126,3.126,0,0,1,8.27,19.059H9.518a1.873,1.873,0,1,0,3.745,0h1.248A3.122,3.122,0,0,1,11.39,22.179Z" transform="translate(-0.846 -2.999)" fill="#00124e"/>
                    </g>
                    </svg>
                    {{__('common.notification')}}</a>
                </li>
                <li>
                    <a class="position-relative d-flex align-items-center log_out" href="{{ route('logout') }}">
                    <svg  width="16.472" height="18" viewBox="0 0 16.472 18">
                    <g id="user-octagon" transform="translate(-2.16 -1.246)">
                        <path id="Path_4175" data-name="Path 4175" d="M10.4,19.246a3.246,3.246,0,0,1-1.632-.435L3.792,15.94A3.286,3.286,0,0,1,2.16,13.111V7.385A3.286,3.286,0,0,1,3.792,4.556L8.764,1.686a3.251,3.251,0,0,1,3.264,0L17,4.556a3.286,3.286,0,0,1,1.632,2.829v5.725A3.286,3.286,0,0,1,17,15.94l-4.972,2.871A3.246,3.246,0,0,1,10.4,19.246Zm0-16.74a2.052,2.052,0,0,0-1,.268L4.42,5.645a2.014,2.014,0,0,0-1,1.741v5.725a2.024,2.024,0,0,0,1,1.741l4.972,2.871a2,2,0,0,0,2.009,0l4.972-2.871a2.014,2.014,0,0,0,1-1.741V7.385a2.024,2.024,0,0,0-1-1.741L11.4,2.774A2.052,2.052,0,0,0,10.4,2.506Z" fill="#00124e"/>
                        <path id="Path_4176" data-name="Path 4176" d="M11.5,10.746a2.578,2.578,0,1,1,2.578-2.578A2.579,2.579,0,0,1,11.5,10.746Zm0-3.9a1.322,1.322,0,1,0,1.322,1.322A1.325,1.325,0,0,0,11.5,6.845Z" transform="translate(-1.102 -0.708)" fill="#00124e"/>
                        <path id="Path_4177" data-name="Path 4177" d="M14.574,16.633A.632.632,0,0,1,13.946,16c0-1.155-1.222-2.1-2.72-2.1s-2.72.946-2.72,2.1A.628.628,0,0,1,7.25,16c0-1.85,1.783-3.356,3.976-3.356S15.2,14.155,15.2,16A.632.632,0,0,1,14.574,16.633Z" transform="translate(-0.83 -1.859)" fill="#00124e"/>
                    </g>
                    </svg>
                    {{__('defaultTheme.log_out')}}</a>
                </li>
            </ul>
        </div>
    </div>

    @include(theme('pages.profile.wallets.components._recharge_modal'))
</div>
