<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['title' => null, 'size' => 'modal_800px']) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['title' => null, 'size' => 'modal_800px']); ?>
<?php foreach (array_filter((['title' => null, 'size' => 'modal_800px']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>
<div class="modal-dialog  <?php echo e($size); ?>  modal-dialog-centered">
    <div class="modal-content">
        <div class="modal-header">
            <h4 class="modal-title"><?php echo e($title); ?></h4>
            <button type="button" class="close" data-dismiss="modal"><i class="ti-close"></i></button>
        </div>

        <?php echo e($slot); ?>

    </div>
</div>
<?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\resources\views/components/backEnd/modal/dialog.blade.php ENDPATH**/ ?>