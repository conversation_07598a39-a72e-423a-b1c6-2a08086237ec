<?php $__env->startSection('mainContent'); ?>
    <?php
        session()->forget('wallet_recharge');
    ?>
    <section class="admin-visitor-area up_st_admin_visitor mb-25">
        <div class="row">
            <?php if(auth()->user()->role->type == 'seller'): ?>
            <div class="col-lg-3 col-md-6">
                <a class="d-block">
                    <div class="white-box single-summery">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h3><?php echo e(__('wallet.pending_earning')); ?></h3>
                            </div>
                            <h1 class="gradient-color2 text-nowrap"><?php echo e(single_price(sellerPendingEarning())); ?></h1>
                        </div>
                    </div>
                </a>
            </div>
            <?php endif; ?>
            <div class="col-lg-3 col-md-6">
                <a class="d-block">
                    <div class="white-box single-summery">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h3><?php echo e(__('wallet.running_balance')); ?></h3>
                            </div>
                            <h1 class="gradient-color2 text-nowrap"><?php echo e(single_price(seller_wallet_balance_running())); ?></h1>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a class="d-block">
                    <div class="white-box single-summery">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h3><?php echo e(__('wallet.pending_balance')); ?></h3>
                            </div>
                            <h1 class="gradient-color2 text-nowrap"><?php echo e(single_price(seller_wallet_balance_pending())); ?></h1>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a class="d-block">
                    <div class="white-box single-summery">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h3><?php echo e(__('wallet.earning_balance')); ?></h3>
                            </div>
                            <h1 class="gradient-color2 text-nowrap"><?php echo e(single_price(auth()->user()->wallet_balances->where('type', 'Sale Payment')->sum('amount'))); ?></h1>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a class="d-block">
                    <div class="white-box single-summery">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h3><?php echo e(__('wallet.withdraw_balance')); ?></h3>
                            </div>
                            <h1 class="gradient-color2 text-nowrap"><?php echo e(single_price(auth()->user()->wallet_balances->where('type', 'Withdraw')->sum('amount'))); ?></h1>
                        </div>
                    </div>
                </a>
            </div>
            <div class="col-lg-3 col-md-6">
                <a class="d-block">
                    <div class="white-box single-summery">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h3><?php echo e(__('wallet.refund_paid')); ?></h3>
                            </div>
                            <h1 class="gradient-color2 text-nowrap"><?php echo e(single_price(auth()->user()->wallet_balances->where('type', 'Refund')->sum('amount'))); ?></h1>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </section>

    <section class="admin-visitor-area up_st_admin_visitor">
        <div class="container-fluid p-0">
            <div class="row justify-content-center">
                <div class="col-lg-12">
                    <div class="box_header common_table_header">
                        <div class="main-title d-md-flex">
                            <h3 class="mb-0 mr-30 mb_xs_15px mb_sm_20px"><?php echo e(__('wallet.transaction_list')); ?></h3>
                            <ul class="d-flex">
                                <?php if(permissionCheck('my-wallet.withdraw_request_sent')): ?>
                                    <li><a data-toggle="modal" data-target="#Recharge_Modal" class="primary-btn radius_30px mr-10 fix-gr-bg" href="#"><i class="ti-plus"></i><?php echo e(__('wallet.recharge_now')); ?></a></li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    <div class="QA_section QA_section_heading_custom check_box_table">
                        <div class="QA_table ">
                            <table class="table" id="myWalletTable">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__('common.sl')); ?></th>
                                        <th><?php echo e(__('common.date')); ?></th>
                                        <th><?php echo e(__('order.txn_id')); ?></th>
                                        <th><?php echo e(__('common.amount')); ?></th>
                                        <th><?php echo e(__('common.type')); ?></th>
                                        <th><?php echo e(__('common.payment_method')); ?></th>
                                        <th><?php echo e(__('common.approval')); ?></th>
                                    </tr>
                                </thead>

                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <input type="hidden" name="sign" class="sign" value="<?php echo e(app('general_setting')->currency_symbol); ?>">
        <input type="hidden" name="current_balance" class="current_balance" value="<?php echo e(auth()->user()->SellerCurrentWalletAmounts); ?>">
        <input type="hidden" name="pending_withdraw_balance" class="pending_withdraw_balance" value="<?php echo e(auth()->user()->SellerPendingWithdrawAmounts); ?>">
        <input type="hidden" name="remaining_balance" class="remaining_balance" value="<?php echo e(auth()->user()->SellerCurrentWalletAmounts - auth()->user()->SellerPendingWithdrawAmounts); ?>">
    </section>
<?php echo $__env->make('wallet::backend.seller.withdraw_requests.withdraw_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('wallet::backend.seller.recharge_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        (function($){
            "use strict";

            $(document).ready(function(){
                let baseUrl = $('#url').val();
                let url = baseUrl + '/wallet/seller/my-wallet-index/get-data';
                $('#myWalletTable').DataTable({
                    processing: true,
                    serverSide: true,
                    stateSave: true,
                    "ajax": ( {
                        url: url
                    }),
                    "initComplete":function(json){

                    },
                    columns: [
                        { data: 'DT_RowIndex', name: 'id',render:function(data){
                            return numbertrans(data)
                        }},
                        { data: 'date', name: 'date' },
                        { data: 'txn_id', name: 'txn_id' },
                        { data: 'amount', name: 'amount' },
                        { data: 'type', name: 'type' },
                        { data: 'payment_method', name: 'payment_method' },
                        { data: 'approval', name: 'approval' }

                    ],

                    bLengthChange: false,
                    "bDestroy": true,
                    language: {
                        search: "<i class='ti-search'></i>",
                        searchPlaceholder: trans('common.quick_search'),
                        paginate: {
                            next: "<i class='ti-arrow-right'></i>",
                            previous: "<i class='ti-arrow-left'></i>"
                        }
                    },
                    dom: 'Bfrtip',
                    buttons: [{
                            extend: 'copyHtml5',
                            text: '<i class="fa fa-files-o"></i>',
                            title: $("#header_title").text(),
                            titleAttr: 'Copy',
                            exportOptions: {
                                columns: ':visible',
                                columns: ':not(:last-child)',
                            }
                        },
                        {
                            extend: 'excelHtml5',
                            text: '<i class="fa fa-file-excel-o"></i>',
                            titleAttr: 'Excel',
                            title: $("#header_title").text(),
                            margin: [10, 10, 10, 0],
                            exportOptions: {
                                columns: ':visible',
                                columns: ':not(:last-child)',
                            },

                        },
                        {
                            extend: 'csvHtml5',
                            text: '<i class="fa fa-file-text-o"></i>',
                            titleAttr: 'CSV',
                            exportOptions: {
                                columns: ':visible',
                                columns: ':not(:last-child)',
                            }
                        },
                        {
                            extend: 'pdfHtml5',
                            text: '<i class="fa fa-file-pdf-o"></i>',
                            title: $("#header_title").text(),
                            titleAttr: 'PDF',
                            exportOptions: {
                                columns: ':visible',
                                columns: ':not(:last-child)',
                            },
                            pageSize: 'A4',
                            margin: [0, 0, 0, 0],
                            alignment: 'center',
                            header: true,

                        },
                        {
                            extend: 'print',
                            text: '<i class="fa fa-print"></i>',
                            titleAttr: 'Print',
                            title: $("#header_title").text(),
                            exportOptions: {
                                columns: ':not(:last-child)',
                            }
                        },
                        {
                            extend: 'colvis',
                            text: '<i class="fa fa-columns"></i>',
                            postfixButtons: ['colvisRestore']
                        }
                    ],
                    columnDefs: [{
                        visible: false
                    }],
                    responsive: true,
                });

                $(document).on('click', '.getNewForm', function(event){
                    event.preventDefault();

                    $("#Withdraw_Modal").modal('show');
                    var sign = $('.sign').val();
                    var current_balance = $('.current_balance').val();
                    var pending_withdraw_balance = $('.pending_withdraw_balance').val();
                    var remaining_balance = $('.remaining_balance').val();
                    var amount = $('.remaining_balance').val();
                    $(".running_balance").text(sign +' '+ current_balance);
                    $(".pending_withdraw_balance").text(sign +' '+ pending_withdraw_balance);
                    $(".remaining_balance").text(sign +' '+ remaining_balance);
                    $(".amount").val(remaining_balance);

                });

                $(document).on('submit', '#withdraw_form', function(event){
                    $('#amount_error_create').text('');
                    var remaining_balance = $('.remaining_balance').val();
                    let withdarw_amount = $('#withdraw_amount_add').val();

                    if(withdarw_amount == '' || withdarw_amount < 1){
                        $('#amount_error_create').text('The Amount is Required.');
                        event.preventDefault();
                    }
                    else if(parseFloat(remaining_balance) < parseFloat(withdarw_amount)){
                        $('#amount_error_create').text('Withdraw Amount Must be Smaller Than Remaining Balance.');
                        event.preventDefault();
                    }
                });

                $(document).on('submit', '#recharge_form', function(event){
                    $('#recharge_amount_error').text('');
                    let recharge_amount = $('#recharge_amount').val();
                    if(recharge_amount == '' || recharge_amount < 1){
                        $('#recharge_amount_error').text('The Amount is Required.');
                        event.preventDefault();
                    }
                });


            });


        })(jQuery);

    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backEnd.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\Modules/Wallet\Resources/views/backend/seller/my_wallet_index.blade.php ENDPATH**/ ?>