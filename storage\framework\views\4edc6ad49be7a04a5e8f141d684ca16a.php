<form id="<?php echo e($form_id); ?>" method="POST" action="" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>
    <div class="row">
        <?php if(isModuleActive('FrontendMultiLang')): ?>
            <div class="col-lg-12">
                <ul class="nav nav-tabs justify-content-start mt-sm-md-20 mb-30 grid_gap_5" role="tablist">
                    <?php $__currentLoopData = $LanguageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="nav-item lang_code" data-id="<?php echo e($language->code); ?>">
                            <a class="nav-link anchore_color <?php if(auth()->user()->lang_code == $language->code): ?> active <?php endif; ?>" href="#<?php echo e($working_process_modal_tab_id); ?><?php echo e($language->code); ?>" role="tab" data-toggle="tab" aria-selected="<?php if(auth()->user()->lang_code == $language->code): ?> true <?php else: ?> false <?php endif; ?>"><?php echo e($language->native); ?> </a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
                <div class="tab-content">
                    <?php $__currentLoopData = $LanguageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div role="tabpanel" class="tab-pane fade <?php if(auth()->user()->lang_code == $language->code): ?> show active <?php endif; ?>" id="<?php echo e($working_process_modal_tab_id); ?><?php echo e($language->code); ?>">
                            <div class="row">                                
                                <div class="col-lg-12 col-md-12 col-sm-12">
                                    <div class="primary_input mb-25">
                                        <label class="primary_input_label" for=""><?php echo e(__('common.title')); ?> <span class="text-danger">*</span></label></label>
                                        <input name="title[<?php echo e($language->code); ?>]" class="primary_input_field title" id="title<?php echo e($language->code); ?>" placeholder="<?php echo e(__('common.title')); ?>" type="text" value="<?php echo e(old('title.'.$language->code)); ?>">
                                    </div>
                                    <span class="text-danger"  id="create_error_title_<?php echo e($language->code); ?>"></span>
                                </div>
                                <div class="col-xl-12">
                                    <div class="primary_input mb-35">
                                        <label class="primary_input_label" for=""><?php echo e(__('common.details')); ?> <span class="text-danger">*</span></label>
                                        <textarea name="description[<?php echo e($language->code); ?>]" placeholder="<?php echo e(__('common.description')); ?>" class="workProcessText" id="description<?php echo e($language->code); ?>"><?php echo e(old('description.'.$language->code)); ?></textarea>
                                    </div>
                                    <span class="text-danger" id="create_error_description_<?php echo e($language->code); ?>"></span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
         <?php else: ?>
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="primary_input mb-25">
                    <label class="primary_input_label" for=""><?php echo e(__('common.title')); ?> <span class="text-danger">*</span></label>
                    <input name="title" class="primary_input_field title" id="title" placeholder="<?php echo e(__('common.title')); ?>" type="text">
                </div>
                <span class="text-danger"  id="create_error_title"></span>
            </div>
            <div class="col-xl-12">
                <div class="primary_input mb-35">
                    <label class="primary_input_label" for=""><?php echo e(__('common.details')); ?> <span class="text-danger">*</span></label>
                    <textarea name="description" placeholder="<?php echo e(__('common.description')); ?>" class="workProcessText" id="description"></textarea>
                </div>
                <span class="text-danger" id="create_error_description"></span>
            </div>
        <?php endif; ?>
        <div class="col-xl-12" id="img_div_for_work">
            <div class="primary_input mb-35">
                <label class="primary_input_label" for=""><?php echo e(__('common.image')); ?> <small class="ml-1">(60x60)px</small> <span class="text-danger">*</span></label>
                <div class="primary_file_uploader">
                    <input class="primary-input" type="text" id="working_process_<?php echo e($form_id); ?>"
                        placeholder="<?php echo e(__('common.browse_image_file')); ?>" readonly="">
                    <button class="" type="button">
                    <label class="primary-btn small fix-gr-bg" for="<?php echo e($form_id); ?>_image"><span
                                class="ripple rippleEffect browse_file_label"></span><?php echo e(__('common.browse')); ?></label>
                        <input name="image" type="file" class="d-none working_process_img" id="<?php echo e($form_id); ?>_image" data-show_name_id="#working_process_<?php echo e($form_id); ?>" data-img_id="#workImgShow_<?php echo e($form_id); ?>">
                    </button>
                    <span class="text-danger" id="create_error_image"></span><br>
                    <img id="workImgShow_<?php echo e($form_id); ?>" class="workProcessImg"
                    src="<?php echo e(showImage('backend/img/default.png')); ?>" alt="">
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="primary_input">
                <label class="primary_input_label" for=""><?php echo e(__('frontendCms.position')); ?> <span
                    class="text-danger">*</span></label>
                <ul id="theme_nav" class="permission_list sms_list ">
                    <li>
                        <label data-id="bg_option"
                               class="primary_checkbox d-flex mr-12">
                            <input name="position" id="position_left" value="1" checked="true" class="active" type="radio">
                            <span class="checkmark"></span>
                        </label>
                        <p><?php echo e(__('frontendCms.left')); ?></p>
                    </li>
                    <li>
                        <label data-id="color_option"
                               class="primary_checkbox d-flex mr-12">
                            <input name="position" value="0" id="position_right"  class="de_active"
                                   type="radio">
                            <span class="checkmark"></span>
                        </label>
                        <p><?php echo e(__('frontendCms.right')); ?></p>
                    </li>
                </ul>
            </div>
            <span class="text-danger" id="create_error_position"></span>
        </div>

        <div class="col-xl-6">
            <div class="primary_input">
                <label class="primary_input_label" for=""><?php echo e(__('common.status')); ?> <span
                    class="text-danger">*</span></label></label>
                <ul id="theme_nav" class="permission_list sms_list ">
                    <li>
                        <label data-id="bg_option"
                               class="primary_checkbox d-flex mr-12">
                            <input name="status" id="status_active" value="1" checked="true" class="active" type="radio">
                            <span class="checkmark"></span>
                        </label>
                        <p><?php echo e(__('common.active')); ?></p>
                    </li>
                    <li>
                        <label data-id="color_option"
                               class="primary_checkbox d-flex mr-12">
                            <input name="status" value="0" id="status_inactive"  class="de_active"
                                   type="radio">
                            <span class="checkmark"></span>
                        </label>
                        <p><?php echo e(__('common.inactive')); ?></p>
                    </li>
                </ul>
            </div>
            <span class="text-danger" id="create_error_status"></span>
        </div>
        <div class="col-lg-12 text-center">
            <div class="d-flex justify-content-center pt_20">
            <button id="<?php echo e($btn_id); ?>" type="submit" class="primary-btn semi_large2 fix-gr-bg"><i
                        class="ti-check"></i>
                        <?php echo e($button_level_name); ?>

                </button>
            </div>
        </div>
    </div>
</form>
<?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\Modules/FrontendCMS\Resources/views/merchant/working_process/form.blade.php ENDPATH**/ ?>