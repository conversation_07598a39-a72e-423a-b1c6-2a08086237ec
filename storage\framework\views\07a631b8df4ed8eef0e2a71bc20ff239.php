<div class="col-lg-8">
    <div class="main-title d-flex">
        <h3 class="mb-3 mr-30"><?php echo e(__('shipping.order_packages')); ?></h3>
    </div>
    <?php
        $items = 0;
        $index  = 0;
        $totalItem = 0;
        $subtotal = 0;
        $actualtotal = 0;
        $shippingtotal = 0;
        $empty_check = 0;
        foreach ($cartData as $data) {
            $empty_check += count($data);
            $items += count($data);
        }
        $gstAmountTotal = 0;
    ?>
    <?php if(count($cartData)): ?>

        <?php $__currentLoopData = $cartData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $cartItems): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $seller = App\Models\User::where('id',$key)->first();
            ?>
            <?php
                $addtional_charge = 0;
                foreach($cartItems as $item){
                    $addtional_charge += $item->product->sku->additional_shipping;
                }
                $index ++;
                $package_wise_shipping = session()->get('inhouseOrderShippingCost')[$key];
                $shippingtotal += $package_wise_shipping['shipping_cost'] + $package_wise_shipping['additional_cost'];
                $package_wise_shipping_cost = $package_wise_shipping['shipping_cost'] + $package_wise_shipping['additional_cost'];
            ?>
                <div class="card mb-10">
                    <div class="card-header">
                        <div class="row">
                            <div class="col-lg-4">
                                <p><?php echo e(__('common.package')); ?> <?php echo e($index); ?> <?php echo e(__('common.of')); ?> <?php echo e($items); ?></p>
                            </div>
                            <div class="col-lg-4">
                                <?php if(isModuleActive('MultiVendor')): ?>
                                <p><?php echo e(__('common.seller')); ?>: <strong><?php if($seller->role->type == 'seller'): ?><?php echo e($seller->first_name .' '. $seller->last_name); ?> <?php else: ?> <?php echo e(app('general_setting')->company_name); ?> <?php endif; ?></strong></p>
                                <?php endif; ?>
                            </div>
                            <div class="col-lg-4">
                                <?php
                                    $sellerShipping = $shippingMethods->where('request_by_user', $key);
                                ?>
                                <select class="primary_select shipping_method" name="shipping_method[]" data-id="<?php echo e($key); ?>" name="product">
                                    <?php $__currentLoopData = $sellerShipping; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $shipping_method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($shipping_method->id); ?>" <?php echo e($package_wise_shipping['shipping_id'] == $shipping_method->id?'selected':''); ?>><?php echo e($shipping_method->method_name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <input type="hidden" name="shipping_cost[]" value="<?php echo e($package_wise_shipping['shipping_cost']); ?>">
                            </div>
                        </div>
                    </div>
                    <?php
                        $shipment_time = $package_wise_shipping['shipping_time'];
                        $shipment_time = explode(" ", $shipment_time);
                        $dayOrOur = $shipment_time[1];
                        $shipment_time = explode("-", $shipment_time[0]);
                        $start_ = $shipment_time[0];
                        $end_ = $shipment_time[1];
                        $date = date('d-m-Y');
                        $start_date = date('d M', strtotime($date. '+ '.$start_.' '.$dayOrOur));
                        $end_date = date('d M', strtotime($date. '+ '.$end_.' '.$dayOrOur));
                    ?>
                    <input type="hidden" name="delivery_date[]" value="<?php if($dayOrOur == 'days' || $dayOrOur == 'Days' ||$dayOrOur == 'Day'): ?><?php echo e(__('shipping.estimated_arrival_date')); ?>: <?php echo e($start_date); ?> - <?php echo e($end_date); ?><?php elseif($dayOrOur == 'hrs' || $dayOrOur == 'Hrs'): ?><?php echo e(__('shipping.estimated_arrival_time')); ?>: <?php echo e($start_date); ?> - <?php echo e($end_date); ?><?php else: ?> <?php endif; ?>">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="QA_section QA_section_heading_custom check_box_table">
                                    <div class="QA_table ">
                                        <div class="overflow-auto min-height-280">
                                            <table class="table">
                                                <thead>
                                                    <tr>
                                                        <th width="20%"><?php echo e(__('product.product_name')); ?></th>
                                                        <th width="20%"><?php echo e(__('product.variation')); ?></th>
                                                        <th width="10%"><?php echo e(__('common.price')); ?></th>
                                                        <th width="20%"><?php echo e(__('common.qty')); ?></th>
                                                        <th width="5%"><?php echo e(__('common.total_price')); ?></th>
                                                        <th width="5%"><?php echo e(__('common.remove')); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                        $packagewiseTax = 0;
                                                    ?>
                                                    <?php $__currentLoopData = $cartItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td class="text-nowrap">
                                                            <?php echo e($item->product->product->product->product_name); ?>

                                                        </td>
                                                        <td class="text-nowrap">
                                                            <?php if($item->product->product->product->product_type == 2): ?>
                                                            <?php $__currentLoopData = $item->product->product_variations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $combination): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php if($combination->attribute->id == 1): ?>
                                                            <p><?php echo e($combination->attribute->name); ?>: <?php echo e($combination->attribute_value->color->name); ?></p>
                                                            <?php else: ?>
                                                            <p><?php echo e($combination->attribute->name); ?>: <?php echo e($combination->attribute_value->value); ?></p>
                                                            <?php endif; ?>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php else: ?>
                                                            <p><?php echo e(__('product.single_product')); ?></p>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td class="text-nowrap">
                                                            <?php if(file_exists(base_path().'/Modules/GST/')): ?>
                                                                <?php if(session()->has('inhouse_order_shipping_address') && app('gst_config')['enable_gst'] == "gst"): ?>
                                                                    <?php
                                                                        if (session()->get('inhouse_order_shipping_address')['is_bill_address'] == 0) {
                                                                            $billing_address = session()->get('inhouse_order_shipping_address');
                                                                            $billing_state = session()->get('inhouse_order_shipping_address')['shipping_state'];
                                                                        }else {
                                                                            $billing_address = session()->get('inhouse_order_billing_address');
                                                                            $billing_address = session()->get('inhouse_order_billing_address')['billing_state'];
                                                                        }
                                                                        $shipping_address = session()->get('inhouse_order_shipping_address');
                                                                    ?>
                                                                    <?php
                                                                        $seller_state = \app\Traits\PickupLocation::pickupPointAddress($seller->id)->state_id;
                                                                    ?>  
                                                                    <?php if($seller_state == $shipping_address['shipping_state']): ?>
                                                                        <?php if($item->product->product->product->gstGroup): ?>
                                                                            <?php
                                                                                $sameStateTaxesGroup = json_decode($item->product->product->product->gstGroup->same_state_gst);
                                                                                $sameStateTaxesGroup = (array) $sameStateTaxesGroup;
                                                                            ?>
                                                                            <?php $__currentLoopData = $sameStateTaxesGroup; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $sameStateTax): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                <?php
                                                                                    $gstAmount = $item->total_price * $sameStateTax / 100;
                                                                                    $gstAmountTotal += $gstAmount;
                                                                                    $packagewiseTax += $gstAmount;
                                                                                ?>
                                                                                
                                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php else: ?>
                                                                            <?php
                                                                                $sameStateTaxes = \Modules\GST\Entities\GstTax::whereIn('id', app('gst_config')['within_a_single_state'])->get();
                                                                            ?>
                                                                            <?php $__currentLoopData = $sameStateTaxes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $sameStateTax): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                <?php
                                                                                    $gstAmount = $item->total_price * $sameStateTax->tax_percentage / 100;
                                                                                    $gstAmountTotal += $gstAmount;
                                                                                    $packagewiseTax += $gstAmount;
                                                                                ?>
                                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php endif; ?>
                                                                    <?php else: ?>
                                                                        <?php if($item->product->product->product->gstGroup): ?>
                                                                            <?php
                                                                                $diffStateTaxesGroup = json_decode($item->product->product->product->gstGroup->outsite_state_gst);
                                                                                $diffStateTaxesGroup = (array) $diffStateTaxesGroup;
                                                                            ?>
                                                                            <?php $__currentLoopData = $diffStateTaxesGroup; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $diffStateTax): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                <?php
                                                                                    $gstAmount = $item->total_price * $diffStateTax / 100;
                                                                                    $gstAmountTotal += $gstAmount;
                                                                                    $packagewiseTax += $gstAmount;
                                                                                ?>
                                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php else: ?>
                                                                            <?php
                                                                                $diffStateTaxes = \Modules\GST\Entities\GstTax::whereIn('id', app('gst_config')['between_two_different_states_or_a_state_and_a_Union_Territory'])->get();
                                                                            ?>
                                                                            <?php $__currentLoopData = $diffStateTaxes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $diffStateTax): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                <?php
                                                                                    $gstAmount = $item->total_price * $diffStateTax->tax_percentage / 100;
                                                                                    $gstAmountTotal += $gstAmount;
                                                                                    $packagewiseTax += $gstAmount;
                                                                                ?>

                                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php endif; ?>
                                                                    <?php endif; ?>
                                                                <?php elseif(app('gst_config')['enable_gst'] == "flat_tax"): ?>
                                                                    <?php if($item->product->product->product->gstGroup): ?>
                                                                        <?php
                                                                            $flatTaxGroup = json_decode($item->product->product->product->gstGroup->same_state_gst);
                                                                            $flatTaxGroup = (array) $flatTaxGroup;
                                                                        ?>
                                                                        <?php $__currentLoopData = $flatTaxGroup; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sameStateTax): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <?php
                                                                                $gstAmount = $item->total_price * $sameStateTax / 100;
                                                                                $gstAmountTotal += $gstAmount;
                                                                                $packagewiseTax += $gstAmount;
                                                                            ?>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    <?php else: ?>
                                                                        <?php
                                                                            $flatTax = \Modules\GST\Entities\GstTax::where('id', app('gst_config')['flat_tax_id'])->first();
                                                                            $gstAmount = $item->total_price * $flatTax->tax_percentage / 100;
                                                                            $gstAmountTotal += $gstAmount;
                                                                            $packagewiseTax += $gstAmount;
                                                                        ?>
                                                                    <?php endif; ?>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                            <?php
                                                                $product = \Modules\Seller\Entities\SellerProductSKU::where('id',$item->product_id)->first();
                                                                $totalItem += $item->qty;
                                                                $subtotal += $product->selling_price * $item->qty;
                                                                $actualtotal += $item->total_price; 
                                                            ?>
                                                            <?php echo e(single_price($item->price)); ?>

                                                            <?php if($item->price != $product->selling_price): ?>
                                                            <del><?php echo e(single_price($product->selling_price)); ?></del>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td class="text-nowrap">
                                                            <input class="primary_input_field product_qty" data-id="<?php echo e($item->id); ?>" type="number" step="1" min="1" name=""
                                                                autocomplete="off" value="<?php echo e($item->qty); ?>">
                                                        </td>
                                                        <td class="text-nowrap">
                                                            <p><?php echo e(single_price($item->total_price)); ?></p>
                                                        </td>
                                                        <td class="text-nowrap"><a href="" data-id="<?php echo e($item->id); ?>" class="deleteCartItem"><i class="ti-trash"></i></a></td>
                                                    </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <input type="hidden" name="packagewiseTax[]" value="<?php echo e($packagewiseTax); ?>">
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php else: ?>
    <p class="no_product"><?php echo e(__('order.no_product_selected')); ?></p>
    <?php endif; ?>
</div>
<input type="hidden" name="number_of_package" value="<?php echo e($items); ?>">
<div class="col-lg-4 mb-50"> 
    <table class="table-borderless clone_line_table">
        <tr>
            <div class="main-title d-flex">
                <h3 class="mb-3 mr-30"><?php echo e(__('common.summary')); ?></h3>
            </div>
        </tr>
        <tr>
            <td><strong class="pr-2"><?php echo e(__('order.total_quantity')); ?></strong></td>
            <td>: <strong class="pl-4"><?php echo e(getNumberTranslate($totalItem)); ?></strong></td>
            <input type="hidden" value="<?php echo e($totalItem); ?>" name="total_quantity">
        </tr>
        <tr>
            <td><strong class="pr-2"><?php echo e(__('order.sub_total')); ?></strong></td>
            <td>: <strong class="pl-4"><?php echo e(single_price($subtotal)); ?></strong></td>
            <input type="hidden" value="<?php echo e($subtotal); ?>" name="sub_total">
        </tr>
        <tr>
            <td><strong class="pr-2"><?php echo e(__('common.discount')); ?></strong></td>
            <td>: <strong class="pl-4">- <?php echo e(single_price($subtotal - $actualtotal)); ?></strong></td>
            <input type="hidden" value="<?php echo e($subtotal - $actualtotal); ?>" name="discount_total">
        </tr>
        <tr>
            <td><strong class="pr-2"><?php echo e(__('order.total_shipping_charge')); ?></strong></td>
            <td>: <strong class="pl-4"><?php echo e(single_price($shippingtotal)); ?></strong></td>
            <input type="hidden" value="<?php echo e($shippingtotal); ?>" name="shipping_charge">
        </tr>
        <tr>
            <td><strong class="pr-2"><?php echo e(__('Total TAX/GST')); ?></strong></td>
            <td>: <strong class="pl-4"><?php echo e(single_price($gstAmountTotal)); ?></strong></td>
            <input type="hidden" value="<?php echo e($gstAmountTotal); ?>" name="gst_tax_total">
        </tr>
        <tr>
            <td><strong class="pr-2"><?php echo e(__('common.grand_total')); ?></strong></td>
            <td>: <strong class="pl-4"><?php echo e(single_price($actualtotal + $shippingtotal  + $gstAmountTotal)); ?></strong></td>
            <input type="hidden" value="<?php echo e($actualtotal + $shippingtotal  + $gstAmountTotal); ?>" name="grand_total">
        </tr>
    </table>
</div>
<?php if(\Session::has('inhouse_order_shipping_address')): ?>
<div class="col-lg-6 offset-lg-3 mt-50">
    <div class="col-lg-12 text-center">
        <button id="submit_btn" type="submit" class="primary-btn fix-gr-bg"
            data-toggle="tooltip" title="" data-original-title="">
            <span class="ti-check"></span>
            <?php echo e(__('order.create_order')); ?> </button>
    </div>
</div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\Modules/InhouseOrder\Resources/views/inhouse_order/components/_product_by_package.blade.php ENDPATH**/ ?>