<form id="formData" action="<?php echo e(route('frontendcms.merchant-content.update')); ?>" method="POST" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>
    <div class="row">
        <input type="hidden" name="id" id="mainId" value="<?php echo e($content->id); ?>">
        <div class="col-xl-12 mb-20">
            <div class="row">
                <?php if(isModuleActive('FrontendMultiLang')): ?>
                    <div class="col-lg-12">
                        <ul class="nav nav-tabs justify-content-start mt-sm-md-20 mb-30 grid_gap_5" role="tablist">
                            <?php $__currentLoopData = $LanguageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="nav-item lang_code" data-id="<?php echo e($language->code); ?>">
                                    <a class="nav-link anchore_color marchent_content <?php if(auth()->user()->lang_code == $language->code): ?> active <?php endif; ?>" data-id="<?php echo e($language->code); ?>" href="#element<?php echo e($language->code); ?>" role="tab" data-toggle="tab" aria-selected="<?php if(auth()->user()->lang_code == $language->code): ?> true <?php else: ?> false <?php endif; ?>"><?php echo e($language->native); ?> </a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                        <div class="tab-content">
                            <?php $__currentLoopData = $LanguageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div role="tabpanel" class="tab-pane fade <?php if(auth()->user()->lang_code == $language->code): ?> show active <?php endif; ?>" id="element<?php echo e($language->code); ?>">
                                    <div class="row">                                
                                        <div class="col-xl-7">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-25">
                                                    <label class="primary_input_label" for="mainTitle"><?php echo e(__('frontendCms.main_title')); ?> <span class="text-danger">*</span></label>
                                                    <input id="mainTitle<?php echo e($language->code); ?>" name="mainTitle[<?php echo e($language->code); ?>]" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(isset($content)?$content->getTranslation('mainTitle',$language->code):old('mainTitle.'.$language->code)); ?>">
                                                    <span class="text-danger" id="error_mainTitle_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-25">
                                                    <label class="primary_input_label" for="subTitle"><?php echo e(__('frontendCms.sub_title')); ?> <span class="text-danger">*</span></label>
                                                    <input id="subTitle<?php echo e($language->code); ?>" name="subTitle[<?php echo e($language->code); ?>]" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(isset($content)?$content->getTranslation('subTitle',$language->code):old('subTitle.'.$language->code)); ?>">
                                                    <span class="text-danger" id="error_subTitle_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                            <div class="col-xl-12 d-none" id="default_lang_<?php echo e($language->code); ?>">
                                                <div class="primary_input mb-25">
                                                    <label class="primary_input_label" for="slug"><?php echo e(__('common.slug')); ?> <span class="text-danger">*</span></label>
                                                    <input id="mainSlug<?php echo e($language->code); ?>" name="slug[<?php echo e($language->code); ?>]" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(old('slug') ? old('slug') : $content->slug); ?>">
                                                    <span class="text-danger" id="error_slug_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                    
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-25">
                                                    <label class="primary_input_label" for="pricing"><?php echo e(__('frontendCms.pricing_slogan')); ?> <span class="text-danger">*</span></label>
                                                    <input id="pricing<?php echo e($language->code); ?>" name="pricing[<?php echo e($language->code); ?>]" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(isset($content)?$content->getTranslation('pricing',$language->code):old('pricing.'.$language->code)); ?>">
                                                    <span class="text-danger" id="error_pricing_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-5">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-35">
                                                    <label class="primary_input_label" for=""><?php echo e(__('frontendCms.main_details')); ?> <span class="text-danger">*</span></label>
                                                    <textarea id="Maindescription<?php echo e($language->code); ?>" name="Maindescription[<?php echo e($language->code); ?>]" class="lms_summernote"><?php echo e(isset($content)?$content->getTranslation('Maindescription',$language->code):old('Maindescription.'.$language->code)); ?></textarea>
                                                    <span class="text-danger" id="error_mainDescription_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="col-xl-7">
                        <div class="col-xl-12">
                            <div class="primary_input mb-25">
                                <label class="primary_input_label" for="mainTitle"><?php echo e(__('frontendCms.main_title')); ?> <span class="text-danger">*</span></label>
                                <input id="mainTitle" name="mainTitle" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(old('mainTitle') ? old('mainTile') : $content->mainTitle); ?>">
                                <span class="text-danger" id="error_mainTitle"></span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="primary_input mb-25">
                                <label class="primary_input_label" for="subTitle"><?php echo e(__('frontendCms.sub_title')); ?> <span class="text-danger">*</span></label>
                                <input id="subTitle" name="subTitle" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(old('subTitle') ? old('subTile') : $content->subTitle); ?>">
                                <span class="text-danger" id="error_subTitle"></span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="primary_input mb-25">
                                <label class="primary_input_label" for="slug"><?php echo e(__('common.slug')); ?> <span class="text-danger">*</span></label>
                                <input id="mainSlug" name="slug" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(old('slug') ? old('slug') : $content->slug); ?>">
                                <span class="text-danger" id="error_slug"></span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="primary_input mb-25">
                                <label class="primary_input_label" for="pricing"><?php echo e(__('frontendCms.pricing_slogan')); ?> <span class="text-danger">*</span></label>
                                <input id="pricing" name="pricing" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(old('pricing') ? old('pricing') : $content->pricing); ?>">
                                <span class="text-danger" id="error_pricing"></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-5">
                        <div class="col-xl-12">
                            <div class="primary_input mb-35">
                                <label class="primary_input_label" for=""><?php echo e(__('frontendCms.main_details')); ?> <span class="text-danger">*</span></label>
                                <textarea id="Maindescription" name="Maindescription" class="lms_summernote"><?php echo e($content->Maindescription); ?></textarea>
                                <span class="text-danger" id="error_mainDescription"></span>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="col-xl-12 mb-20">
            <div class="row">
                <?php if(isModuleActive('FrontendMultiLang')): ?>
                    <div class="col-lg-12">
                        <div class="tab-content">
                            <?php $__currentLoopData = $LanguageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div role="tabpanel" class="tab-pane fade default_lang <?php if(auth()->user()->lang_code == $language->code): ?> show active <?php endif; ?>" id="benefitelement<?php echo e($language->code); ?>">
                                    <div class="row">                                
                                        <div class="col-xl-7">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-25">
                                                    <label class="primary_input_label" for="benifitTitle"><?php echo e(__('frontendCms.benifit_title')); ?> <span class="text-danger">*</span></label>
                                                    <input id="benifitTitle<?php echo e($language->code); ?>" name="benifitTitle[<?php echo e($language->code); ?>]" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(isset($content)?$content->getTranslation('benifitTitle',$language->code):old('benifitTitle.'.$language->code)); ?>">
                                                    <span class="text-danger" id="error_benifitTitle_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                            <div class="col-xl-12">
                                                <label class="primary_input_label" for="benefit"><?php echo e(__('frontendCms.benefit_list')); ?></label>
                                                <div id="benefit_table">
                                                    <?php echo $__env->make('frontendcms::merchant.benefit.list', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-5">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-35">
                                                    <label class="primary_input_label" for=""><?php echo e(__('frontendCms.benefits_details')); ?> <span class="text-danger">*</span></label>
                                                    <textarea id="benifitDescription<?php echo e($language->code); ?>" name="benifitDescription[<?php echo e($language->code); ?>]" class="lms_summernote"><?php echo e(isset($content)?$content->getTranslation('benifitDescription',$language->code):old('benifitDescription.'.$language->code)); ?></textarea>
                                                    <span class="text-danger" id="error_benifitDescription_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="col-xl-7">
                        <div class="col-xl-12">
                            <div class="primary_input mb-25">
                                <label class="primary_input_label" for="benifitTitle"><?php echo e(__('frontendCms.benifit_title')); ?> <span class="text-danger">*</span></label>
                                <input id="benifitTitle" name="benifitTitle" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(old('benifitTitle') ? old('benifitTile') : $content->benifitTitle); ?>">
                                <span class="text-danger" id="error_benifitTitle"></span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <label class="primary_input_label" for="benefit"><?php echo e(__('frontendCms.benefit_list')); ?></label>
                            <div id="benefit_table">
                                <?php echo $__env->make('frontendcms::merchant.benefit.list', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-5">
                        <div class="col-xl-12">
                            <div class="primary_input mb-35">
                                <label class="primary_input_label" for=""><?php echo e(__('frontendCms.benefits_details')); ?> <span class="text-danger">*</span></label>
                                <textarea id="benifitDescription" name="benifitDescription" class="lms_summernote"><?php echo e($content->benifitDescription); ?></textarea>
                                <span class="text-danger" id="error_benifitDescription"></span>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="col-xl-12 mb-20">
            <div class="row">
                <?php if(isModuleActive('FrontendMultiLang')): ?>
                    <div class="col-lg-12">
                        <div class="tab-content">
                            <?php $__currentLoopData = $LanguageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div role="tabpanel" class="tab-pane fade default_lang <?php if(auth()->user()->lang_code == $language->code): ?> show active <?php endif; ?>" id="howitworkelement<?php echo e($language->code); ?>">
                                    <div class="row">                                
                                        <div class="col-xl-7">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-25">
                                                    <label class="primary_input_label" for="howitworkTitle"><?php echo e(__('frontendCms.how_it_work_title')); ?> <span class="text-danger">*</span></label>
                                                    <input id="howitworkTitle<?php echo e($language->code); ?>" name="howitworkTitle[<?php echo e($language->code); ?>]" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(isset($content)?$content->getTranslation('howitworkTitle',$language->code):old('howitworkTitle.'.$language->code)); ?>">
                                                    <span class="text-danger" id="error_howitworkTitle_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                            <div class="col-xl-12">
                                                <label class="primary_input_label" for="howitworkTitle"><?php echo e(__('frontendCms.how_it_work_list')); ?></label>
                                                <div id="work_table">
                                                    <?php echo $__env->make('frontendcms::merchant.working_process.list', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-5">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-35">
                                                    <label class="primary_input_label" for="howitworkDescription"><?php echo e(__('frontendCms.how_it_work_details')); ?> <span class="text-danger">*</span></label>
                                                    <textarea id="howitworkDescription<?php echo e($language->code); ?>" name="howitworkDescription[<?php echo e($language->code); ?>]" class="lms_summernote"><?php echo e(isset($content)?$content->getTranslation('howitworkDescription',$language->code):old('howitworkDescription.'.$language->code)); ?></textarea>
                                                    <span class="text-danger" id="error_howitworkDescription_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="col-xl-7">
                        <div class="col-xl-12">
                            <div class="primary_input mb-25">
                                <label class="primary_input_label" for="howitworkTitle"><?php echo e(__('frontendCms.how_it_work_title')); ?> <span class="text-danger">*</span></label>
                                <input id="howitworkTitle" name="howitworkTitle" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(old('howitworkTitle') ? old('howitworkTitle') : $content->howitworkTitle); ?>">
                                <span class="text-danger" id="error_howitworkTitle"></span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <label class="primary_input_label" for="howitworkTitle"><?php echo e(__('frontendCms.how_it_work_list')); ?></label>
                            <div id="work_table">
                                <?php echo $__env->make('frontendcms::merchant.working_process.list', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-5">
                        <div class="col-xl-12">
                            <div class="primary_input mb-35">
                                <label class="primary_input_label" for="howitworkDescription"><?php echo e(__('frontendCms.how_it_work_details')); ?> <span class="text-danger">*</span></label>
                                <textarea id="howitworkDescription" name="howitworkDescription" class="lms_summernote"><?php echo e($content->howitworkDescription); ?></textarea>
                                <span class="text-danger" id="error_howitworkDescription"></span>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="col-xl-12 mb-20">
            <div class="row">
                <?php if(isModuleActive('FrontendMultiLang')): ?>
                    <div class="col-lg-12">
                        <div class="tab-content">
                            <?php $__currentLoopData = $LanguageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div role="tabpanel" class="tab-pane fade default_lang <?php if(auth()->user()->lang_code == $language->code): ?> show active <?php endif; ?>" id="pricingelement<?php echo e($language->code); ?>">
                                    <div class="row">
                                        <div class="col-xl-7">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-25">
                                                    <label class="primary_input_label" for="pricingTitle"><?php echo e(__('frontendCms.pricing_title')); ?> <span class="text-danger">*</span></label>
                                                    <input id="pricingTitle<?php echo e($language->code); ?>" name="pricingTitle[<?php echo e($language->code); ?>]" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(isset($content)?$content->getTranslation('pricingTitle',$language->code):old('pricingTitle.'.$language->code)); ?>">
                                                    <span class="text-danger" id="error_pricingTitle_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-5">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-35">
                                                    <label class="primary_input_label" for="pricingDescription"><?php echo e(__('frontendCms.pricing_details')); ?> <span class="text-danger">*</span></label>
                                                    <textarea id="pricingDescription<?php echo e($language->code); ?>" name="pricingDescription[<?php echo e($language->code); ?>]" class="lms_summernote"><?php echo e(isset($content)?$content->getTranslation('pricingDescription',$language->code):old('pricingDescription.'.$language->code)); ?></textarea>
                                                    <span class="text-danger" id="error_pricingDescription_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <div class="row">
                            <div class="col-xl-7">
                                <div class="col-xl-12">
                                    <div class="primary_input">
                                        <label class="primary_input_label" for=""><?php echo e(__('frontendCms.subscription_crone_job_url')); ?></label>
                                        <input id="subscription_crone_job" name="subscription_crone_job" class="primary_input_field" readonly type="text" value="<?php echo e(route('subscription_crone_job')); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="col-xl-7">
                        <div class="col-xl-12">
                            <div class="primary_input mb-25">
                                <label class="primary_input_label" for="pricingTitle"><?php echo e(__('frontendCms.pricing_title')); ?> <span class="text-danger">*</span></label>
                                <input id="pricingTitle" name="pricingTitle" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(old('pricingTitle') ? old('pricingTitle') : $content->pricingTitle); ?>">
                                <span class="text-danger" id="error_pricingTitle"></span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="primary_input">
                                <label class="primary_input_label" for=""><?php echo e(__('frontendCms.subscription_crone_job_url')); ?></label>
                                <input id="subscription_crone_job" name="subscription_crone_job" class="primary_input_field" readonly type="text" value="<?php echo e(route('subscription_crone_job')); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-5">
                        <div class="col-xl-12">
                            <div class="primary_input mb-35">
                                <label class="primary_input_label" for="pricingDescription"><?php echo e(__('frontendCms.pricing_details')); ?> <span class="text-danger">*</span></label>
                                <textarea id="pricingDescription" name="pricingDescription" class="lms_summernote"><?php echo e($content->pricingDescription); ?></textarea>
                                <span class="text-danger" id="error_pricingDescription"></span>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="col-xl-12 mb-20">
            <div class="row">
                <?php if(isModuleActive('FrontendMultiLang')): ?>
                    <div class="col-lg-12">
                        <div class="tab-content">
                            <?php $__currentLoopData = $LanguageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div role="tabpanel" class="tab-pane fade default_lang <?php if(auth()->user()->lang_code == $language->code): ?> show active <?php endif; ?>" id="sellerRegistrationelement<?php echo e($language->code); ?>">
                                    <div class="row">
                                        <div class="col-xl-7">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-25">
                                                    <label class="primary_input_label" for="pricingTitle"><?php echo e(__('frontendCms.seller_registration_title_for_first_page')); ?><span class="text-danger">*</span></label>
                                                    <input id="sellerRegistrationTitle<?php echo e($language->code); ?>" name="sellerRegistrationTitle[<?php echo e($language->code); ?>]" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(isset($content)?$content->getTranslation('sellerRegistrationTitle',$language->code):old('sellerRegistrationTitle.'.$language->code)); ?>">
                                                    <span class="text-danger" id="error_sellerRegistrationTitle_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-5">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-35">
                                                    <label class="primary_input_label" for="pricingDescription"><?php echo e(__('frontendCms.description')); ?><span class="text-danger">*</span></label>
                                                    <textarea id="sellerRegistrationDescription<?php echo e($language->code); ?>" name="sellerRegistrationDescription[<?php echo e($language->code); ?>]" class="lms_summernote"><?php echo e(isset($content)?$content->getTranslation('sellerRegistrationDescription',$language->code):old('sellerRegistrationDescription.'.$language->code)); ?></textarea>
                                                    <span class="text-danger" id="error_sellerRegistrationDescription_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="col-xl-7">
                        <div class="col-xl-12">
                            <div class="primary_input mb-25">
                                <label class="primary_input_label" for="pricingTitle"><?php echo e(__('frontendCms.seller_registration_title_for_first_page')); ?><span class="text-danger">*</span></label>
                                <input id="sellerRegistrationTitle" name="sellerRegistrationTitle" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(old('sellerRegistrationTitle') ? old('sellerRegistrationTitle') : $content->sellerRegistrationTitle); ?>">
                                <span class="text-danger" id="error_sellerRegistrationTitle"></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-5">
                        <div class="col-xl-12">
                            <div class="primary_input mb-35">
                                <label class="primary_input_label" for="pricingDescription"><?php echo e(__('frontendCms.description')); ?><span class="text-danger">*</span></label>
                                <textarea id="sellerRegistrationDescription" name="sellerRegistrationDescription" class="lms_summernote"><?php echo e($content->sellerRegistrationDescription); ?></textarea>
                                <span class="text-danger" id="error_sellerRegistrationDescription"></span>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="col-xl-12 mb-20">
            <div class="row">
                <?php if(isModuleActive('FrontendMultiLang')): ?>
                    <div class="col-lg-12">
                        <div class="tab-content">
                            <?php $__currentLoopData = $LanguageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div role="tabpanel" class="tab-pane fade default_lang <?php if(auth()->user()->lang_code == $language->code): ?> show active <?php endif; ?>" id="faqelement<?php echo e($language->code); ?>">
                                    <div class="row">
                                        <div class="col-xl-7">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-25">
                                                    <label class="primary_input_label" for="queryTitle"><?php echo e(__('frontendCms.faq_title')); ?> <span class="text-danger">*</span></label>
                                                    <input id="faqTitle<?php echo e($language->code); ?>" name="faqTitle[<?php echo e($language->code); ?>]" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(isset($content)?$content->getTranslation('faqTitle',$language->code):old('faqTitle.'.$language->code)); ?>">
                                                    <span class="text-danger" id="error_faqTitle_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                            <div class="col-xl-12">
                                                <label class="primary_input_label" for="howitworkTitle"><?php echo e(__('frontendCms.faq_list')); ?></label>
                                                <div id="faq_table">
                                                    <?php echo $__env->make('frontendcms::merchant.faq.list', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-5">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-35">
                                                    <label class="primary_input_label" for="queryDescription"><?php echo e(__('frontendCms.faq_details')); ?> <span class="text-danger">*</span></label>
                                                    <textarea id="faqDescription<?php echo e($language->code); ?>" name="faqDescription[<?php echo e($language->code); ?>]" class="lms_summernote"><?php echo e(isset($content)?$content->getTranslation('faqDescription',$language->code):old('faqDescription.'.$language->code)); ?></textarea>
                                                    <span class="text-danger" id="error_faqDescription_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="col-xl-7">
                        <div class="col-xl-12">
                            <div class="primary_input mb-25">
                                <label class="primary_input_label" for="faqTitle"><?php echo e(__('frontendCms.faq_title')); ?> <span class="text-danger">*</span></label>
                                <input id="faqTitle" name="faqTitle" class="primary_input_field" placeholder="-" type="text" value="<?php echo e($content->faqTitle); ?>">
                                <span class="text-danger" id="error_faqTitle"></span>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <label class="primary_input_label" for="howitworkTitle"><?php echo e(__('frontendCms.faq_list')); ?></label>
                            <div id="faq_table">
                                <?php echo $__env->make('frontendcms::merchant.faq.list', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-5">
                        <div class="col-xl-12">
                            <div class="primary_input mb-35">
                                <label class="primary_input_label" for="faqDescription"><?php echo e(__('frontendCms.faq_details')); ?> <span class="text-danger">*</span></label>
                                <textarea id="faqDescription" name="faqDescription" class="lms_summernote"><?php echo e($content->faqDescription); ?></textarea>
                                <span class="text-danger" id="error_faqDescription"></span>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="col-xl-12">
            <div class="row">
                <?php if(isModuleActive('FrontendMultiLang')): ?>
                    <div class="col-lg-12">
                        <div class="tab-content">
                            <?php $__currentLoopData = $LanguageList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div role="tabpanel" class="tab-pane fade default_lang <?php if(auth()->user()->lang_code == $language->code): ?> show active <?php endif; ?>" id="queryelement<?php echo e($language->code); ?>">
                                    <div class="row">
                                        <div class="col-xl-7">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-25">
                                                    <label class="primary_input_label" for="queryTitle"><?php echo e(__('frontendCms.query_title')); ?> <span class="text-danger">*</span></label>
                                                    <input id="queryTitle<?php echo e($language->code); ?>" name="queryTitle[<?php echo e($language->code); ?>]" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(isset($content)?$content->getTranslation('queryTitle',$language->code):old('queryTitle.'.$language->code)); ?>">
                                                    <span class="text-danger" id="error_queryTitle_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-5">
                                            <div class="col-xl-12">
                                                <div class="primary_input mb-35">
                                                    <label class="primary_input_label" for="queryDescription"><?php echo e(__('frontendCms.query_details')); ?> <span class="text-danger">*</span></label>
                                                    <textarea id="queryDescription<?php echo e($language->code); ?>" name="queryDescription[<?php echo e($language->code); ?>]" class="lms_summernote"><?php echo e(isset($content)?$content->getTranslation('queryDescription',$language->code):old('queryDescription.'.$language->code)); ?></textarea>
                                                    <span class="text-danger" id="error_queryDescription_<?php echo e($language->code); ?>"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="col-xl-7">
                        <div class="col-xl-12">
                            <div class="primary_input mb-25">
                                <label class="primary_input_label" for="queryTitle"><?php echo e(__('frontendCms.query_title')); ?> <span class="text-danger">*</span></label>
                                <input id="queryTitle" name="queryTitle" class="primary_input_field" placeholder="-" type="text" value="<?php echo e(old('queryTitle') ? old('queryTitle') : $content->queryTitle); ?>">
                                <span class="text-danger" id="error_queryTitle"></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-5">
                        <div class="col-xl-12">
                            <div class="primary_input mb-35">
                                <label class="primary_input_label" for="queryDescription"><?php echo e(__('frontendCms.query_details')); ?> <span class="text-danger">*</span></label>
                                <textarea id="queryDescription" name="queryDescription" class="lms_summernote"><?php echo e($content->queryDescription); ?></textarea>
                                <span class="text-danger" id="error_queryDescription"></span>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php if(permissionCheck('frontendcms.merchant-content.update')): ?>
            <div class="col-lg-12 text-center">
                <div class="d-flex justify-content-center">
                    <button id="mainSubmit" class="primary-btn semi_large2  fix-gr-bg mr-1" id="save_button_parent" type="submit" dusk="Update"><i class="ti-check"></i><?php echo e(__('common.update')); ?></button>
                </div>
            </div>
        <?php endif; ?>
    </div>
</form>
<?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\Modules/FrontendCMS\Resources/views/merchant/components/form.blade.php ENDPATH**/ ?>