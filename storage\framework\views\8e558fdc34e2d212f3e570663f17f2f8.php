
<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['action' => null]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['action' => null]); ?>
<?php foreach (array_filter((['action' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>
<div class="col-lg-12 text-center">

    <div class="d-flex justify-content-center pt_20">
        <?php if($action == 'delete'): ?>
            <button type="submit" class="primary-btn fix-gr-bg submit"><i
                    class="ti-check"></i><?php echo e(__('common.delete')); ?>

            </button>
            <button type="button" class="primary-btn fix-gr-bg submitting disabled"
                    style="display: none;"> <?php echo e(__('common.deleting')); ?>

            </button>
        <?php else: ?>
        <button type="submit" class="primary-btn fix-gr-bg submit"><i
                class="ti-check"></i><?php echo e(__('common.submit')); ?>

        </button>
        <button type="button" class="primary-btn fix-gr-bg submitting disabled"
                style="display: none;"> <?php echo e(__('common.submitting')); ?>

        </button>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\tow-three-ld-last-copy\resources\views/components/backEnd/modal/footer.blade.php ENDPATH**/ ?>