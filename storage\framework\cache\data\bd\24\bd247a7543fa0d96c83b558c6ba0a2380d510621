9999999999O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:8:{i:0;O:43:"Modules\FooterSetting\Entities\FooterWidget":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"footer_widgets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:1;s:7:"user_id";N;s:4:"name";s:8:"About Us";s:4:"slug";s:8:"about-us";s:8:"category";s:1:"1";s:4:"page";s:1:"1";s:7:"section";s:1:"1";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:11:" * original";a:11:{s:2:"id";i:1;s:7:"user_id";N;s:4:"name";s:8:"About Us";s:4:"slug";s:8:"about-us";s:8:"category";s:1:"1";s:4:"page";s:1:"1";s:7:"section";s:1:"1";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"pageData";O:40:"Modules\FrontendCMS\Entities\DynamicPage":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"dynamic_pages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:1;s:7:"user_id";N;s:5:"title";s:8:"About Us";s:4:"slug";s:8:"about-us";s:11:"description";s:3950:"<div class="row">
        <div class="col-sm-6 ui-resizable" data-type="container-content"></div>
        <div class="col-sm-6 ui-resizable" data-type="container-content"></div>
    </div><div class="row">
        <div class="col-sm-4 ui-resizable" data-type="container-content"><div data-type="component-photo">
                <div class="photo-panel">
                    <img src="http://localhost/tow-three-ld/Modules/PageBuilder/Resources/assets/keditor/snippets/img/somewhere_bangladesh_squared.jpg" width="100%" height="" style="display: inline-block;" class="img-circle">
                </div>
            </div><div data-type="component-text">
<h3 style="text-align: center;">Lorem ipsum</h3>

<p style="text-align: center;">Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vel, alias, temporibus? Vero natus modi ipsa debitis, accusamus accusantium cum quam. Saepe atque quisquam pariatur voluptatem expedita reprehenderit et vitae.</p>
</div>
</div>
        <div class="col-sm-4 ui-resizable" data-type="container-content"><div data-type="component-photo">
                <div class="photo-panel">
                    <img src="http://localhost/tow-three-ld/Modules/PageBuilder/Resources/assets/keditor/snippets/img/wellington_newzealand_squared.jpg" width="100%" height="" style="display: inline-block;" class="img-circle">
                </div>
            </div><div data-type="component-text">
<h3 style="text-align: center;">Lorem ipsum</h3>

<p style="text-align: center;">Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quaerat, aut, earum. Quod, debitis, delectus. Maxime eius ipsam sit dolorum perspiciatis obcaecati consectetur, explicabo reprehenderit repellat tempore eos ducimus!</p>
</div>
</div>
        <div class="col-sm-4 ui-resizable" data-type="container-content"><div data-type="component-photo">
                <div class="photo-panel">
                    <img src="http://localhost/tow-three-ld/Modules/PageBuilder/Resources/assets/keditor/snippets/img/yenbai_vietnam_squared.jpg" width="100%" height="" style="display: inline-block;" class="img-circle">
                </div>
            </div><div data-type="component-text">
<h3 style="text-align: center;">Lorem ipsum</h3>

<p style="text-align: center;">Lorem ipsum dolor sit amet, consectetur adipisicing elit. Nihil voluptatibus dicta corrupti aliquam, natus voluptatem pariatur quidem nostrum nisi corporis id ratione exercitationem et recusandae incidunt assumenda soluta qui odit.</p>
</div>
</div>
    </div><div class="row">
            <div class="col-sm-12 ui-resizable" data-type="container-content"><div data-type="component-text">
<div class="about_counter_area section_spacing6">
<div class="container">
<div class="row">
<div class="col-xl-3 col-lg-3 col-md-6">
<div class="about_countBox mb_30 text-center">
<h3>11,000+</h3>

<p>Client Worldwide</p>
</div>
</div>

<div class="col-xl-3 col-lg-3 col-md-6">
<div class="about_countBox mb_30 text-center">
<h3>42M+</h3>

<p>Successful Project</p>
</div>
</div>

<div class="col-xl-3 col-lg-3 col-md-6">
<div class="about_countBox mb_30 text-center">
<h3>8.3M+</h3>

<p>Work Employed</p>
</div>
</div>

<div class="col-xl-3 col-lg-3 col-md-6">
<div class="about_countBox mb_30 text-center">
<h3>295</h3>

<p>Planning Services</p>
</div>
</div>
</div>
</div>
</div>
</div>
<div data-type="component-text">
<div class="about_cta_area">
<div class="container">
<div class="row">
<div class="col-xl-5 col-lg-6 offset-xl-2 offset-lg-1 col-md-9">
<div class="section__title2"><span class="d-block font_14 f_w_500 text-uppercase secondary_text lh-1 mb_23">Quick Parcel Delivery, Form $25</span>
<h3>Hurry Up! Furniture Teville Chair Up To 60% Off.</h3>

<p class="font_16 f_w_400 priamry_text ">This tall toy storage basket is perfect to gift for her</p>
<a class="amaz_primary_btn style2" href="#">Shop collection</a></div>
</div>
</div>
</div>
</div>
</div>
</div>
        </div>";s:6:"status";i:1;s:9:"is_static";i:0;s:15:"is_page_builder";i:1;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-07-03 11:56:15";}s:11:" * original";a:11:{s:2:"id";i:1;s:7:"user_id";N;s:5:"title";s:8:"About Us";s:4:"slug";s:8:"about-us";s:11:"description";s:3950:"<div class="row">
        <div class="col-sm-6 ui-resizable" data-type="container-content"></div>
        <div class="col-sm-6 ui-resizable" data-type="container-content"></div>
    </div><div class="row">
        <div class="col-sm-4 ui-resizable" data-type="container-content"><div data-type="component-photo">
                <div class="photo-panel">
                    <img src="http://localhost/tow-three-ld/Modules/PageBuilder/Resources/assets/keditor/snippets/img/somewhere_bangladesh_squared.jpg" width="100%" height="" style="display: inline-block;" class="img-circle">
                </div>
            </div><div data-type="component-text">
<h3 style="text-align: center;">Lorem ipsum</h3>

<p style="text-align: center;">Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vel, alias, temporibus? Vero natus modi ipsa debitis, accusamus accusantium cum quam. Saepe atque quisquam pariatur voluptatem expedita reprehenderit et vitae.</p>
</div>
</div>
        <div class="col-sm-4 ui-resizable" data-type="container-content"><div data-type="component-photo">
                <div class="photo-panel">
                    <img src="http://localhost/tow-three-ld/Modules/PageBuilder/Resources/assets/keditor/snippets/img/wellington_newzealand_squared.jpg" width="100%" height="" style="display: inline-block;" class="img-circle">
                </div>
            </div><div data-type="component-text">
<h3 style="text-align: center;">Lorem ipsum</h3>

<p style="text-align: center;">Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quaerat, aut, earum. Quod, debitis, delectus. Maxime eius ipsam sit dolorum perspiciatis obcaecati consectetur, explicabo reprehenderit repellat tempore eos ducimus!</p>
</div>
</div>
        <div class="col-sm-4 ui-resizable" data-type="container-content"><div data-type="component-photo">
                <div class="photo-panel">
                    <img src="http://localhost/tow-three-ld/Modules/PageBuilder/Resources/assets/keditor/snippets/img/yenbai_vietnam_squared.jpg" width="100%" height="" style="display: inline-block;" class="img-circle">
                </div>
            </div><div data-type="component-text">
<h3 style="text-align: center;">Lorem ipsum</h3>

<p style="text-align: center;">Lorem ipsum dolor sit amet, consectetur adipisicing elit. Nihil voluptatibus dicta corrupti aliquam, natus voluptatem pariatur quidem nostrum nisi corporis id ratione exercitationem et recusandae incidunt assumenda soluta qui odit.</p>
</div>
</div>
    </div><div class="row">
            <div class="col-sm-12 ui-resizable" data-type="container-content"><div data-type="component-text">
<div class="about_counter_area section_spacing6">
<div class="container">
<div class="row">
<div class="col-xl-3 col-lg-3 col-md-6">
<div class="about_countBox mb_30 text-center">
<h3>11,000+</h3>

<p>Client Worldwide</p>
</div>
</div>

<div class="col-xl-3 col-lg-3 col-md-6">
<div class="about_countBox mb_30 text-center">
<h3>42M+</h3>

<p>Successful Project</p>
</div>
</div>

<div class="col-xl-3 col-lg-3 col-md-6">
<div class="about_countBox mb_30 text-center">
<h3>8.3M+</h3>

<p>Work Employed</p>
</div>
</div>

<div class="col-xl-3 col-lg-3 col-md-6">
<div class="about_countBox mb_30 text-center">
<h3>295</h3>

<p>Planning Services</p>
</div>
</div>
</div>
</div>
</div>
</div>
<div data-type="component-text">
<div class="about_cta_area">
<div class="container">
<div class="row">
<div class="col-xl-5 col-lg-6 offset-xl-2 offset-lg-1 col-md-9">
<div class="section__title2"><span class="d-block font_14 f_w_500 text-uppercase secondary_text lh-1 mb_23">Quick Parcel Delivery, Form $25</span>
<h3>Hurry Up! Furniture Teville Chair Up To 60% Off.</h3>

<p class="font_16 f_w_400 priamry_text ">This tall toy storage basket is perfect to gift for her</p>
<a class="amaz_primary_btn style2" href="#">Shop collection</a></div>
</div>
</div>
</div>
</div>
</div>
</div>
        </div>";s:6:"status";i:1;s:9:"is_static";i:0;s:15:"is_page_builder";i:1;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-07-03 11:56:15";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}i:1;O:43:"Modules\FooterSetting\Entities\FooterWidget":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"footer_widgets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:2;s:7:"user_id";N;s:4:"name";s:4:"Blog";s:4:"slug";s:4:"blog";s:8:"category";s:1:"1";s:4:"page";s:1:"3";s:7:"section";s:1:"1";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:11:" * original";a:11:{s:2:"id";i:2;s:7:"user_id";N;s:4:"name";s:4:"Blog";s:4:"slug";s:4:"blog";s:8:"category";s:1:"1";s:4:"page";s:1:"3";s:7:"section";s:1:"1";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"pageData";O:40:"Modules\FrontendCMS\Entities\DynamicPage":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"dynamic_pages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:3;s:7:"user_id";N;s:5:"title";s:4:"Blog";s:4:"slug";s:4:"blog";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:11:" * original";a:11:{s:2:"id";i:3;s:7:"user_id";N;s:5:"title";s:4:"Blog";s:4:"slug";s:4:"blog";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}i:2;O:43:"Modules\FooterSetting\Entities\FooterWidget":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"footer_widgets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:3;s:7:"user_id";N;s:4:"name";s:9:"Dashboard";s:4:"slug";s:17:"profile/dashboard";s:8:"category";s:1:"2";s:4:"page";s:2:"17";s:7:"section";s:1:"2";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:11:" * original";a:11:{s:2:"id";i:3;s:7:"user_id";N;s:4:"name";s:9:"Dashboard";s:4:"slug";s:17:"profile/dashboard";s:8:"category";s:1:"2";s:4:"page";s:2:"17";s:7:"section";s:1:"2";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"pageData";O:40:"Modules\FrontendCMS\Entities\DynamicPage":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"dynamic_pages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:17;s:7:"user_id";N;s:5:"title";s:9:"Dashboard";s:4:"slug";s:17:"profile/dashboard";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:11:" * original";a:11:{s:2:"id";i:17;s:7:"user_id";N;s:5:"title";s:9:"Dashboard";s:4:"slug";s:17:"profile/dashboard";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}i:3;O:43:"Modules\FooterSetting\Entities\FooterWidget":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"footer_widgets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:4;s:7:"user_id";N;s:4:"name";s:10:"My Profile";s:4:"slug";s:7:"profile";s:8:"category";s:1:"2";s:4:"page";s:1:"5";s:7:"section";s:1:"2";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:11:" * original";a:11:{s:2:"id";i:4;s:7:"user_id";N;s:4:"name";s:10:"My Profile";s:4:"slug";s:7:"profile";s:8:"category";s:1:"2";s:4:"page";s:1:"5";s:7:"section";s:1:"2";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"pageData";O:40:"Modules\FrontendCMS\Entities\DynamicPage":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"dynamic_pages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:5;s:7:"user_id";N;s:5:"title";s:10:"My Account";s:4:"slug";s:7:"profile";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:11:" * original";a:11:{s:2:"id";i:5;s:7:"user_id";N;s:5:"title";s:10:"My Account";s:4:"slug";s:7:"profile";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}i:4;O:43:"Modules\FooterSetting\Entities\FooterWidget":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"footer_widgets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:5;s:7:"user_id";N;s:4:"name";s:8:"My Order";s:4:"slug";s:18:"my-purchase-orders";s:8:"category";s:1:"2";s:4:"page";s:1:"6";s:7:"section";s:1:"2";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:11:" * original";a:11:{s:2:"id";i:5;s:7:"user_id";N;s:4:"name";s:8:"My Order";s:4:"slug";s:18:"my-purchase-orders";s:8:"category";s:1:"2";s:4:"page";s:1:"6";s:7:"section";s:1:"2";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"pageData";O:40:"Modules\FrontendCMS\Entities\DynamicPage":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"dynamic_pages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:6;s:7:"user_id";N;s:5:"title";s:9:"My Orders";s:4:"slug";s:18:"my-purchase-orders";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:11:" * original";a:11:{s:2:"id";i:6;s:7:"user_id";N;s:5:"title";s:9:"My Orders";s:4:"slug";s:18:"my-purchase-orders";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}i:5;O:43:"Modules\FooterSetting\Entities\FooterWidget":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"footer_widgets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:6;s:7:"user_id";N;s:4:"name";s:14:"Help & Contact";s:4:"slug";s:10:"contact-us";s:8:"category";s:1:"3";s:4:"page";s:2:"13";s:7:"section";s:1:"3";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:11:" * original";a:11:{s:2:"id";i:6;s:7:"user_id";N;s:4:"name";s:14:"Help & Contact";s:4:"slug";s:10:"contact-us";s:8:"category";s:1:"3";s:4:"page";s:2:"13";s:7:"section";s:1:"3";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"pageData";O:40:"Modules\FrontendCMS\Entities\DynamicPage":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"dynamic_pages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:13;s:7:"user_id";N;s:5:"title";s:17:"Help & Contact Us";s:4:"slug";s:10:"contact-us";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:11:" * original";a:11:{s:2:"id";i:13;s:7:"user_id";N;s:5:"title";s:17:"Help & Contact Us";s:4:"slug";s:10:"contact-us";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}i:6;O:43:"Modules\FooterSetting\Entities\FooterWidget":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"footer_widgets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:7;s:7:"user_id";N;s:4:"name";s:11:"Track Order";s:4:"slug";s:11:"track-order";s:8:"category";s:1:"3";s:4:"page";s:2:"14";s:7:"section";s:1:"3";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:11:" * original";a:11:{s:2:"id";i:7;s:7:"user_id";N;s:4:"name";s:11:"Track Order";s:4:"slug";s:11:"track-order";s:8:"category";s:1:"3";s:4:"page";s:2:"14";s:7:"section";s:1:"3";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"pageData";O:40:"Modules\FrontendCMS\Entities\DynamicPage":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"dynamic_pages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:14;s:7:"user_id";N;s:5:"title";s:12:"Track Orders";s:4:"slug";s:11:"track-order";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:11:" * original";a:11:{s:2:"id";i:14;s:7:"user_id";N;s:5:"title";s:12:"Track Orders";s:4:"slug";s:11:"track-order";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}i:7;O:43:"Modules\FooterSetting\Entities\FooterWidget":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"footer_widgets";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:8;s:7:"user_id";N;s:4:"name";s:17:"Return & Exchange";s:4:"slug";s:15:"return-exchange";s:8:"category";s:1:"3";s:4:"page";s:2:"21";s:7:"section";s:1:"3";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:11:" * original";a:11:{s:2:"id";i:8;s:7:"user_id";N;s:4:"name";s:17:"Return & Exchange";s:4:"slug";s:15:"return-exchange";s:8:"category";s:1:"3";s:4:"page";s:2:"21";s:7:"section";s:1:"3";s:9:"is_static";i:0;s:6:"status";i:1;s:10:"created_at";s:19:"2025-06-12 22:00:21";s:10:"updated_at";s:19:"2025-06-12 22:00:21";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"pageData";O:40:"Modules\FrontendCMS\Entities\DynamicPage":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:13:"dynamic_pages";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:11:{s:2:"id";i:21;s:7:"user_id";N;s:5:"title";s:17:"Return & Exchange";s:4:"slug";s:15:"return-exchange";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:11:" * original";a:11:{s:2:"id";i:21;s:7:"user_id";N;s:5:"title";s:17:"Return & Exchange";s:4:"slug";s:15:"return-exchange";s:11:"description";s:19:"initial description";s:6:"status";i:1;s:9:"is_static";i:1;s:15:"is_page_builder";i:0;s:6:"module";N;s:10:"created_at";s:19:"2025-06-12 21:00:00";s:10:"updated_at";s:19:"2025-06-12 21:00:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:12:"translatable";a:0:{}s:20:" * translationLocale";N;}}s:28:" * escapeWhenCastingToString";b:0;}