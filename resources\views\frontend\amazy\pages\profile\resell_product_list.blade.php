@extends('frontend.amazy.layouts.app')
@section('styles')

@section('title')
    {{ __('My Resell Products') }}
@endsection

@section('content')
    <div class="amazy_dashboard_area dashboard_bg section_spacing6">
        <div class="container">
            <div class="row">
                <div class="col-xl-3 col-lg-4">
                    @include('frontend.amazy.pages.profile.partials._menu')
                </div>
                <div class="col-xl-9 col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="font_20 f_w_700 mb-0">{{ __('My Resell Products') }}</h3>
                        </div>
                        <div class="card-body">
                            @if (isset($resellProducts) && $resellProducts->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Resell Price</th>
                                                <th>Status</th>
                                                <th>Created Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($resellProducts as $product)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            @if ($product->thumbnail_image_source)
                                                                <img src="{{ showImage($product->thumbnail_image_source) }}"
                                                                    alt="{{ $product->product_name }}"
                                                                    class="img-thumbnail me-3"
                                                                    style="width: 50px; height: 50px; object-fit: cover;">
                                                            @endif
                                                            <div>
                                                                <h6 class="mb-0">{{ $product->product_name }}</h6>
                                                                @if($product->resell_description)
                                                                    <small class="text-muted">{{ \Illuminate\Support\Str::limit($product->resell_description, 50) }}</small>
                                                                @endif
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <strong class="text-primary">{{ single_price($product->resell_price) }}</strong>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-success">{{ __('Active') }}</span>
                                                    </td>
                                                    <td>{{ $product->created_at->format('M d, Y') }}</td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <button type="button" class="btn btn-sm btn-outline-primary"
                                                                onclick="viewProduct({{ $product->id }})">
                                                                <i class="fas fa-eye"></i> {{ __('View') }}
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                                onclick="deleteProduct({{ $product->id }})">
                                                                <i class="fas fa-trash"></i> {{ __('Delete') }}
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                @if ($resellProducts->lastPage() > 1)
                                    <div class="d-flex justify-content-center mt-4">
                                        {{ $resellProducts->links() }}
                                    </div>
                                @endif
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">{{ __('No Resell Products Found') }}</h5>
                                    <p class="text-muted">{{ __('You haven\'t submitted any products for resale yet.') }}
                                    </p>
                                    <a href="{{ route('frontend.my_purchase_order_list') }}" class="btn btn-primary">
                                        {{ __('Go to My Orders') }}
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {
            $('[data-toggle="tooltip"]').tooltip();
        });

        function viewProduct(productId) {
            // You can implement a modal or redirect to product view
            window.open('/product/' + productId, '_blank');
        }

        function deleteProduct(productId) {
            if (confirm('Are you sure you want to delete this resell product?')) {
                // You can implement delete functionality here
                $.ajax({
                    url: '/resell-product/delete/' + productId,
                    type: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        location.reload();
                    },
                    error: function(xhr) {
                        alert('Error deleting product. Please try again.');
                    }
                });
            }
        }
    </script>
@endpush
